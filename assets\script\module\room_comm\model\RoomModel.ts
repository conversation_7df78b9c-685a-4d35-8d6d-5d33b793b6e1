import { Pve_Info, Shop_Goods } from "auto/tableCodes/schema";
import { Tween, tween } from "cc";
import { cfg, <PERSON><PERSON><PERSON> } from "core";
import { GButton, GTextField } from "fairygui-cc";
import { GBaseModel, LangMgr, ModelDecorator, TipBase, TipDirection, TipInfo, UITipsManager } from "game-base";
import { CommonTextTip } from "../../comm/view/tips/CommonTextTip";

@ModelDecorator()
export class RoomModel extends GBaseModel {
    public static className: string = "RoomModel";
    private tipBase: TipBase;
    private readonly DOUBLEEXPCARD: number = 11998;
    private readonly DOUBLEMERITCARD: number = 11997;
    private readonly PREVENTKICKCARD: number = 11996;

    private indexNums: number[] = [];
    private currentIndex: number = 0;
    public readonly MAX_ROOM_COUNT: number = 8;
    public takeAlongPropsArray: number[] = [0, 0, 0];
    public readonly PVP_TIPS_TWEEN_TAG: number = 10000;
    public readonly PVE_TIPS_TWEEN_TAG: number = 10001;
    //public static readonly NO_SUITABLE_ROOMS: string = "暂时没有合适的房间，请稍后再试。";

    //
    public getPropBean(id: number): Shop_Goods.Shop_GoodsBean {
        var propBean = cfg.tables.Shop_GoodsTable.get(id);
        if (propBean !== null) {
            return propBean;
        }
        return null;
    }

    /**
     * 获取pve房间信息
     * @param id 房间id
     * @returns 房间信息
     */
    public getPveRoomInfo(id: number): Pve_Info.Pve_InfoBean {
        //id与map文件夹名一致
        var bean = cfg.tables.Pve_InfoTable.get(id);
        if (bean !== null) {
            return bean;
        }
        return null;
    }

    /**
     * 获取pve等级限制条件
     * @param field 等级限制字段
     * @returns 等级限制数组
     * @example 1-10|11-20|21-30 => [1-10, 11-20, 21-30]
     */
    public getPveLvLimitArray(field: string): string[] {
        if (field == null || field == undefined) return [];
        return field.split("|");
    }

    /**
     * 获取pve等级限制的左边界
     * @param array 等级限制数组
     * @returns 左边界数组
     * @example [1-10, 11-20, 21-30] => [1, 11, 21]
     */
    public getPveLvLimitNumberArray(array: string[]): number[] {
        if (array.length == 0) return [];

        return array.map(section => {
            const leftPart = section.split('-')[0];
            return Number(leftPart);
        });
    }

    /**
     * 文本换行
     * @param input 输入文本
     * @param maxPerLine 每行最大字符数
     * @returns 多少个字符换行后的文本
     * @example "1234567890" => "123456\n7890"
     */
    public wrapText(input: string, maxPerLine: number = 12): string {
        if (!input) return "";

        let result: string[] = [];
        let count = 0;

        for (const c of input) {
            result.push(c);
            count++;
            if (count % maxPerLine === 0) {
                result.push("\n");
            }
        }

        return result.join("").replace(/\n$/, "");
    }

    public getSkillId(index: number): number {
        if (index == 0) {
            return this.DOUBLEEXPCARD;
        } else if (index == 1) {
            return this.DOUBLEMERITCARD;
        } else if (index == 2) {
            return this.PREVENTKICKCARD;
        } else {
            //配表中没找到，后续添加
            return 0;
        }
    }

    private resetNumbers(): void {
        this.indexNums = [];

        for (let i = 1; i <= 41; i++) {
            this.indexNums.push(i);
        }

        for (let i = this.indexNums.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [this.indexNums[i], this.indexNums[j]] = [this.indexNums[j], this.indexNums[i]];
        }

        this.currentIndex = 0;
    }

    private getNextRandomNumber(): number {
        if (this.currentIndex >= this.indexNums.length) {
            this.resetNumbers();
        }

        const num = this.indexNums[this.currentIndex];
        this.currentIndex++;

        return num;
    }

    public getFriendshipTips(): string {
        let id = this.getNextRandomNumber();
        let bean = cfg.tables.MovingNotificationTable.get(id);
        if (bean !== null) {
            return LangMgr.inst().getText(bean.TipsContent);
        }

        return "";
    }

    public friendshipTipsTween(content: GTextField, height: number, tag: number): void {
        if (content == null || content == undefined) {
            return;
        }
        content.setPosition(content.x, height);
        content.text = this.getFriendshipTips();

        let t1 = tween(content)
            .to(1.0, { y: content.y - height }, { easing: "linear" })
            .delay(10);
        let t2 = tween(content)
            .to(1.0, { y: -height }, { easing: "linear" })
            .call(() => {
                content.setPosition(content.x, height);
                content.text = this.getFriendshipTips();
            });
        tween(content).tag(tag).sequence(t1, t2).repeatForever().start();
    }

    public stopTween(tag: number): void {
        Tween.stopAllByTag(tag);
    }

    public showTextTip(btn: GButton, content: string, tipDir: TipDirection = TipDirection.TOP_CENTER): void {
        if (btn == null || btn == undefined) return;

        if (this.tipBase != null) {
            this.tipBase.visibility = true;
        }
        var tipInfo = new TipInfo();
        tipInfo.uiName = CommonTextTip;
        tipInfo.target = btn;
        tipInfo.tipsDirections = [tipDir];
        tipInfo.params = content;
        UITipsManager.inst()
            .showTip(tipInfo)
            .then(uiTips => {
                this.tipBase = uiTips;
            });
    }

    public HideTextTip(): void {
        if (this.tipBase != null) {
            this.tipBase.visibility = false;
        }
    }

    //C_tank_view_common_RoomIIPropTip_Energy
    public getTextFormat(name: string, des: string, power: number): string {
        var temp = `[b][color=#FFFF99]${name}[/b][/color]` +
            "\n" +
            `[b]${LangMgr.inst().getText(LKey.C_ddt_pets_skillTipLost)}[/b]: ` +
            `[color=#F2C834]${power}[/color]` +
            LangMgr.inst().getText(LKey.C_energy) +
            "\n" +
            `[b]${LangMgr.inst().getText(LKey.C_ddt_pets_skillTipDesc)}[/b]: ` +
            "\n" +
            des;
        return `[size=15]${temp}[/size]`;
    }

    onRegister(): void {
        this.resetNumbers();
    }

    onRemove(): void { }
}

export enum Entrance {
    ENTER,
    FIND,
}

export enum RoomType {
    PVP,
    PVE,
    //挑战赛
    CHALLENGE,
    //精英赛
    ELITE,
}
