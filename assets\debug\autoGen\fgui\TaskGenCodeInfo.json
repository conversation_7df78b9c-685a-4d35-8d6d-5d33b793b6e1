[{"type": "GComponent", "name": "QuitGameUI", "res": "ui://nkg9d0brh81620", "children": [{"type": "GComponent", "name": "bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GButton", "name": "confirmBtn"}, {"type": "GButton", "name": "cancelBtn"}, {"type": "GList", "name": "list"}]}, {"type": "GComponent", "name": "TaskItem", "res": "ui://nkg9d0brh81621", "children": [{"type": "GList", "name": "list"}]}, {"type": "GComponent", "name": "MoreUI", "res": "ui://nkg9d0brq2v11v", "children": [{"type": "GComponent", "name": "bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GList", "name": "list"}]}, {"type": "GComponent", "name": "RewardItem", "res": "ui://nkg9d0brq2v11w", "children": [{"type": "GComponent", "name": "comp", "res": "ui://nkg9d0brh26l1q", "resName": "TaskRewardItem", "package": "Task"}]}, {"type": "GComponent", "name": "TaskUI", "res": "ui://nkg9d0brun8c0", "children": [{"type": "GComponent", "name": "bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GComponent", "name": "taskRewardComp", "res": "ui://nkg9d0brun8c1g", "resName": "TaskRewardComp", "package": "Task", "children": [{"type": "GList", "name": "fixedCurrencyRewardList"}, {"type": "GList", "name": "fixedItemRewardList"}, {"type": "GComponent", "name": "optionalReward", "res": "ui://nkg9d0brun8c1j", "resName": "OptionalRewardComp", "package": "Task", "children": [{"type": "GList", "name": "optionalRewardList"}]}]}, {"type": "GList", "name": "taskList"}, {"type": "GButton", "name": "getRewardBtn"}, {"type": "GList", "name": "taskDescList"}]}, {"type": "GComponent", "name": "TaskFirstTabItem", "res": "ui://nkg9d0brun8c1d", "children": [{"type": "GList", "name": "subTaskItemList"}]}, {"type": "GComponent", "name": "TaskDescComp", "res": "ui://nkg9d0brun8c1f", "children": [{"type": "GList", "name": "list"}]}, {"type": "GComponent", "name": "TaskRewardComp", "res": "ui://nkg9d0brun8c1g", "children": [{"type": "GList", "name": "fixedCurrencyRewardList"}, {"type": "GList", "name": "fixedItemRewardList"}, {"type": "GComponent", "name": "optionalReward", "res": "ui://nkg9d0brun8c1j", "resName": "OptionalRewardComp", "package": "Task", "children": [{"type": "GList", "name": "optionalRewardList"}]}]}, {"type": "GComponent", "name": "OptionalRewardComp", "res": "ui://nkg9d0brun8c1j", "children": [{"type": "GList", "name": "optionalRewardList"}]}]