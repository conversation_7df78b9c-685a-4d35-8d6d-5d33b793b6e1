{
    "prettier.tabWidth": 4,
    "prettier.printWidth": 160, // 增大行宽限制
    "prettier.proseWrap": "preserve", // 保持原始换行
    "prettier.arrowParens": "avoid",
    "prettier.bracketSpacing": true, // 对象/数组括号与内容间保留空格
    "prettier.bracketSameLine": true,
    "prettier.trailingComma": "all",
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    // 保存时自动格式化
    "editor.formatOnSave": true,
    // 自动修复ESLint问题
    // "editor.codeActionsOnSave": {
    // "source.fixAll.eslint": true
    // },
    // 缩进设置（2空格或4空格）
    "editor.tabSize": 4,
    // 使用空格代替Tab
    "editor.insertSpaces": true,
    // 自动移除行尾空格
    "files.trimTrailingWhitespace": true,
    // 文件末尾自动添加换行符
    "files.insertFinalNewline": true,
    "files.exclude": {
        "**/*.meta": true,
        "**/.git": true,
        "**/.svn": true,
        "**/.js": true,
        ".gitignore": true,
        "build": true,
        "library": true,
        "local": true,
        "native": true,
        "profiles": true,
        "temp": true,
        "extensions": true,
        "node_modules": true,
        "dist": true,
        "assets/resources": true
    }
}
