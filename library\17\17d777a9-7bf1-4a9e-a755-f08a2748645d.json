{"__type__": "cc.Json<PERSON>set", "_name": "Hot_SpringGenCodeInfo", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "json": [{"type": "GComponent", "name": "HotSpringHallView", "res": "ui://14g2uuvtql5u0", "children": [{"type": "GButton", "name": "n1"}, {"type": "GComponent", "name": "TurnPage", "res": "ui://14g2uuvtql5uh", "resName": "TurnPage", "package": "Hot_Spring", "children": [{"type": "GButton", "name": "pageNextBtn"}, {"type": "GButton", "name": "pagePrevBtn"}, {"type": "GButton", "name": "pageFirstBtn"}, {"type": "GButton", "name": "pageLastBtn"}]}, {"type": "GList", "name": "RoomList"}, {"type": "GButton", "name": "QuickJoinBtn"}, {"type": "GButton", "name": "CreateRoomBtn"}]}, {"type": "GComponent", "name": "TurnPage", "res": "ui://14g2uuvtql5uh", "children": [{"type": "GButton", "name": "pageNextBtn"}, {"type": "GButton", "name": "pagePrevBtn"}, {"type": "GButton", "name": "pageFirstBtn"}, {"type": "GButton", "name": "pageLastBtn"}]}, {"type": "GComponent", "name": "HotSpringSceneView", "res": "ui://14g2uuvtql5up", "children": [{"type": "GButton", "name": "ShowName"}, {"type": "GButton", "name": "ShowBubble"}, {"type": "GButton", "name": "ShowPlayers"}, {"type": "GComponent", "name": "<PERSON><PERSON><PERSON><PERSON>", "res": "ui://14g2uuvtrbmvv", "resName": "<PERSON><PERSON><PERSON><PERSON>", "package": "Hot_Spring", "children": [{"type": "GButton", "name": "ReturnBtn"}, {"type": "GButton", "name": "FoldBtn"}, {"type": "GList", "name": "MenuBtnList"}]}]}, {"type": "GComponent", "name": "<PERSON><PERSON><PERSON><PERSON>", "res": "ui://14g2uuvtrbmvv", "children": [{"type": "GButton", "name": "ReturnBtn"}, {"type": "GButton", "name": "FoldBtn"}, {"type": "GList", "name": "MenuBtnList"}]}]}