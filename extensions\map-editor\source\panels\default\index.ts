/* eslint-disable vue/one-component-per-file */
import { Asset } from '@cocos/creator-types/editor/packages/asset-db/@types/protected';
import { error } from 'console';
import { readFileSync } from 'fs-extra';
import path, { join } from 'path';
import { createApp, App, defineComponent } from 'vue';
const panelDataMap = new WeakMap<any, App>();
import packageJSON from '../../../package.json';

// module.paths.push(join(Editor.App.path, 'node_modules'));
// import * as cc from 'cc';
import { Node } from 'cc';
/**
 * @zh 如果希望兼容 3.3 之前的版本可以使用下方的代码
 * @en You can add the code below if you want compatibility with versions prior to 3.3
 */
// Editor.Panel.define = Editor.Panel.define || function(options: any) { return options }
module.exports = Editor.Panel.define({
    listeners: {
        show() { console.log('show'); },
        hide() { console.log('hide'); },
    },
    template: readFileSync(join(__dirname, '../../../static/template/default/index.html'), 'utf-8'),
    style: readFileSync(join(__dirname, '../../../static/style/default/index.css'), 'utf-8'),
    $: {
        app: '#app',
        mapPath: '#mapPath',
    },
    methods: {
    },
    ready() {
        if (this.$.app) {
            const app = createApp({});
            app.config.compilerOptions.isCustomElement = (tag) => tag.startsWith('ui-');
           
            app.component('MapCreator', defineComponent({
                data() {
                    return {
                        mapPath: '' // 新增地图路径数据
                    };
                }, 
                methods: {
                    async selectMapPath() {
                        // const config: any = {
                        //     type: 'directory',
                        //     title: '选择地图资源路径',
                        //     path: Editor.Project.path + "\\assets\\res\\image\\map",
                        // };
                        // const data = await Editor.Dialog.select(config);

                        // if (!data.cancelled) {
                        //     let path:string = data.filePaths[0];
                        //     console.log('选择的路径:', path);
                        //     this.mapPath = path;
                        // }
                        const result = await Editor.Message.request('scene', 'execute-scene-script', {
                            name : packageJSON.name,
                            method : 'rotateCamera',
                            args : [10]
                        });
                        console.log('result========>', result);
                    },
                    async generatePrefab() {
                        if (!this.mapPath) {
                            const config: any = {
                                title: 'Error',
                                detail: '没有选择地图资源路径',
                            };
                            await Editor.Dialog.error('Error', config);
                            return;
                        }

                        console.log('开始生成Prefab');
                        try {
                            // 创建根节点

                            // const mainCamera = cc.Director.instance.getScene()?.getChildByName('Main Camera');


                            console.log('开始生成Prefab1111111111');
                            // const rootNode = new Node("xx");
                            // console.log('rootNode', rootNode);
                            console.log('开始生成Prefab2222');

                            
                            // // 创建子节点
                            // const backgroundNode = new cc.Node('Background');
                            // const excavateLandNode = new cc.Node('Excavate_Land');
                            // const noExcavateLandNode = new cc.Node('No_Excavate_Land');
                            // const livingNode = new cc.Node('Living');
                            
                            // // 添加子节点到根节点
                            // rootNode.addChild(backgroundNode);
                            // rootNode.addChild(excavateLandNode);
                            // rootNode.addChild(noExcavateLandNode);
                            // rootNode.addChild(livingNode);

                            // 加载纹理并创建Sprite组件
                            // const backTexture = await this.loadTexture(path.join(this.mapPath, 'back.jpg'));
                            // const backSprite = backgroundNode.addComponent(cc.Sprite);
                            // backSprite.spriteFrame = new cc.SpriteFrame(backTexture);

                            // const deadTexture = await this.loadTexture(path.join(this.mapPath, 'dead.png'));
                            // const deadSprite = noExcavateLandNode.addComponent(cc.Sprite);
                            // deadSprite.spriteFrame = new cc.SpriteFrame(deadTexture);

                            // const foreTexture = await this.loadTexture(path.join(this.mapPath, 'fore.png'));
                            // const foreSprite = excavateLandNode.addComponent(cc.Sprite);
                            // foreSprite.spriteFrame = new cc.SpriteFrame(foreTexture);

                            // // 保存为Prefab
                            // const prefab = new cc.Prefab();
                            // prefab.data = rootNode;
                            
                            // const prefabPath = path.join('assets/maps', `${path.basename(this.mapPath)}.prefab`);
                            // await Editor.Message.request('asset-db', 'create-asset', {
                            //     type: 'prefab',
                            //     path: prefabPath,
                            //     data: prefab
                            // });

                            Editor.Dialog.info('Prefab生成成功');
                        } catch (err) {
                            console.error('生成Prefab失败:', err);
                            Editor.Dialog.error("生成Prefab失败");
                        }
                    },

                    // async loadTexture(texturePath: string): Promise<cc.Texture2D> {
                    //     return new Promise((resolve, reject) => {
                    //         cc.assetManager.load(texturePath, cc.Texture2D, (err, texture) => {
                    //             if (err) reject(err);
                    //             else resolve(texture);
                    //         });
                    //     });
                    // }
                },
                template: readFileSync(join(__dirname, '../../../static/template/vue/mapcreator.html'), 'utf-8'),
            }));
            app.mount(this.$.app);
            panelDataMap.set(this, app);
        }
    },
    beforeClose() { },
    close() {
        const app = panelDataMap.get(this);
        if (app) {
            app.unmount();
        }
    },
});
