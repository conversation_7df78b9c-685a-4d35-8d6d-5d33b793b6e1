[{"type": "GComponent", "name": "FightUIOld", "res": "ui://gytxm6z79puls", "children": [{"type": "GComponent", "name": "touchLayer", "res": "ui://gytxm6z7glb01n", "resName": "TouchLayer", "package": "Fight"}, {"type": "GComponent", "name": "smallMapUI", "res": "ui://gytxm6z7glb01o", "resName": "SmallMapUI", "package": "Fight", "children": [{"type": "GComponent", "name": "Small<PERSON>ayer", "res": "ui://gytxm6z7mk3v2w", "resName": "Small<PERSON>ayer", "package": "Fight"}, {"type": "GButton", "name": "CloseBtn"}, {"type": "GButton", "name": "setBtn"}, {"type": "GComponent", "name": "countdownOverUI", "res": "ui://gytxm6z7xxgetcf", "resName": "CountdownOverUI", "package": "Fight"}]}, {"type": "GComponent", "name": "vaneUI", "res": "ui://gytxm6z7igth2g", "resName": "VaneUI", "package": "Fight", "children": [{"type": "GComponent", "name": "effectContainer", "res": "ui://gytxm6z7krv95wd3", "resName": "EmptyCmp", "package": "Fight"}]}, {"type": "GComponent", "name": "thumbnailUI", "res": "ui://gytxm6z7oazc34", "resName": "ThumbnailUI", "package": "Fight", "children": [{"type": "GList", "name": "team1List"}, {"type": "GList", "name": "team2List"}, {"type": "GComponent", "name": "boss", "res": "ui://gytxm6z7i0gq4h", "resName": "BossThumbanail", "package": "Fight", "children": [{"type": "GComponent", "name": "progress", "res": "ui://gytxm6z7i0gq4j", "resName": "BossHeadProgress", "package": "Fight"}]}]}, {"type": "GButton", "name": "fireBtnUI"}, {"type": "GComponent", "name": "firePowerView", "res": "ui://gytxm6z7lra11h", "resName": "FirePowerUI", "package": "Fight", "children": [{"type": "GComponent", "name": "lastPowerPro", "res": "ui://gytxm6z7lra11i", "resName": "ProgressBar1", "package": "Fight"}, {"type": "GComponent", "name": "powerPro", "res": "ui://gytxm6z7lra11j", "resName": "ProgressBar2", "package": "Fight"}, {"type": "GComponent", "name": "anim", "res": "ui://gytxm6z7krv95wd3", "resName": "EmptyCmp", "package": "Fight"}]}, {"type": "GList", "name": "petSkillUI"}, {"type": "GList", "name": "skillUI"}, {"type": "GList", "name": "usedpropUI"}, {"type": "GComponent", "name": "dander<PERSON>", "res": "ui://gytxm6z7fzee2o", "resName": "DanderUI", "package": "Fight", "children": [{"type": "GComponent", "name": "<PERSON><PERSON><PERSON><PERSON>", "res": "ui://gytxm6z7kt9p44", "resName": "DanderPro", "package": "Fight"}, {"type": "GButton", "name": "powBtn"}]}, {"type": "GComponent", "name": "operationUI", "res": "ui://gytxm6z7ee7s1k", "resName": "WalkUI", "package": "Fight", "children": [{"type": "GButton", "name": "touchArea"}]}, {"type": "GComponent", "name": "selfStatusUI", "res": "ui://gytxm6z7ee7s1l", "resName": "SelfStatus", "package": "Fight", "children": [{"type": "GComponent", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "res": "ui://gytxm6z79pul1e", "resName": "<PERSON><PERSON><PERSON><PERSON>", "package": "Fight"}, {"type": "GComponent", "name": "playerEnergyPro", "res": "ui://gytxm6z79pul1c", "resName": "playerEnergy", "package": "Fight"}, {"type": "GComponent", "name": "petEnergyPro", "res": "ui://gytxm6z7rwij26", "resName": "petSnergyPro", "package": "Fight"}]}, {"type": "GComponent", "name": "formulaUI", "res": "ui://gytxm6z7p1dp53", "resName": "FightFireFormulaUI", "package": "Fight", "children": [{"type": "GButton", "name": "leftBtn"}, {"type": "GButton", "name": "rightBtn"}]}, {"type": "GButton", "name": "formulaBtn"}, {"type": "GButton", "name": "deputyPropUI"}, {"type": "GButton", "name": "flyPropUI"}, {"type": "GComponent", "name": "fireSignrView", "res": "ui://gytxm6z7au9ctc7", "resName": "FireSignUI", "package": "Fight", "children": [{"type": "GButton", "name": "firePowerTouchView"}]}, {"type": "GButton", "name": "targetBtn"}, {"type": "GComponent", "name": "mapTag", "res": "ui://gytxm6z7wrlh2q", "resName": "MapTagUI", "package": "Fight"}, {"type": "GComponent", "name": "countdownUI", "res": "ui://gytxm6z7igth2e", "resName": "Countdown", "package": "Fight", "children": [{"type": "GButton", "name": "passBtn"}]}, {"type": "GComponent", "name": "propUI", "res": "ui://gytxm6z7fzee2j", "resName": "PropUI", "package": "Fight", "children": [{"type": "GList", "name": "propList1"}]}, {"type": "GComponent", "name": "autoManagView", "res": "ui://gytxm6z7s58ebd", "resName": "AutoManagUI", "package": "Fight"}, {"type": "GList", "name": "soulSkillUI"}, {"type": "GButton", "name": "freeCameraBtn"}, {"type": "GButton", "name": "alphaButton"}, {"type": "GButton", "name": "cameraAlignBtn"}, {"type": "GButton", "name": "autoFightBtn"}, {"type": "GButton", "name": "accelerationBtn"}, {"type": "GButton", "name": "skipLocalFightBtn"}]}, {"type": "GComponent", "name": "FireSignUI", "res": "ui://gytxm6z7au9ctc7", "children": [{"type": "GButton", "name": "firePowerTouchView"}]}, {"type": "GComponent", "name": "WalkUI", "res": "ui://gytxm6z7ee7s1k", "children": [{"type": "GButton", "name": "touchArea"}]}, {"type": "GComponent", "name": "SelfStatus", "res": "ui://gytxm6z7ee7s1l", "children": [{"type": "GComponent", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "res": "ui://gytxm6z79pul1e", "resName": "<PERSON><PERSON><PERSON><PERSON>", "package": "Fight"}, {"type": "GComponent", "name": "playerEnergyPro", "res": "ui://gytxm6z79pul1c", "resName": "playerEnergy", "package": "Fight"}, {"type": "GComponent", "name": "petEnergyPro", "res": "ui://gytxm6z7rwij26", "resName": "petSnergyPro", "package": "Fight"}]}, {"type": "GComponent", "name": "RoleInfo", "res": "ui://gytxm6z7fqf65whh", "children": [{"type": "GComponent", "name": "hpBar", "res": "ui://gytxm6z7fqf65whi", "resName": "RoleHpBar", "package": "Fight"}]}, {"type": "GComponent", "name": "PropUI", "res": "ui://gytxm6z7fzee2j", "children": [{"type": "GList", "name": "propList1"}]}, {"type": "GComponent", "name": "DanderUI", "res": "ui://gytxm6z7fzee2o", "children": [{"type": "GComponent", "name": "<PERSON><PERSON><PERSON><PERSON>", "res": "ui://gytxm6z7kt9p44", "resName": "DanderPro", "package": "Fight"}, {"type": "GButton", "name": "powBtn"}]}, {"type": "GComponent", "name": "SmallMapUI", "res": "ui://gytxm6z7glb01o", "children": [{"type": "GComponent", "name": "Small<PERSON>ayer", "res": "ui://gytxm6z7mk3v2w", "resName": "Small<PERSON>ayer", "package": "Fight"}, {"type": "GButton", "name": "CloseBtn"}, {"type": "GButton", "name": "setBtn"}, {"type": "GComponent", "name": "countdownOverUI", "res": "ui://gytxm6z7xxgetcf", "resName": "CountdownOverUI", "package": "Fight"}]}, {"type": "GComponent", "name": "ShootAngle_pc", "res": "ui://gytxm6z7hnlz5wfh", "children": [{"type": "GComponent", "name": "limitAnglePanel", "res": "ui://gytxm6z7u4wm5whe", "resName": "LimitAnglePanel_pc", "package": "Fight", "children": [{"type": "GComponent", "name": "circle", "res": "ui://gytxm6z7hnlz5wfi", "resName": "Circle_pc", "package": "Fight"}, {"type": "GComponent", "name": "arrow", "res": "ui://gytxm6z7hnlz5wfj", "resName": "Arrow_pc", "package": "Fight"}]}]}, {"type": "GComponent", "name": "FlySkill", "res": "ui://gytxm6z7hnlz5wfn", "children": [{"type": "GButton", "name": "skill1"}, {"type": "GButton", "name": "skill2"}]}, {"type": "GComponent", "name": "QuickSkill_Pc", "res": "ui://gytxm6z7hnlz5wfp", "children": [{"type": "GButton", "name": "skill1"}, {"type": "GButton", "name": "skill2"}, {"type": "GButton", "name": "skill3"}]}, {"type": "GComponent", "name": "PetSkill_pc", "res": "ui://gytxm6z7hnlz5wfq", "children": [{"type": "GComponent", "name": "magicBar", "res": "ui://gytxm6z7hnlz5wgb", "resName": "AngerBar", "package": "Fight"}, {"type": "GList", "name": "petSkillList"}, {"type": "GButton", "name": "arrowBtn"}]}, {"type": "GComponent", "name": "BombSkill_pc", "res": "ui://gytxm6z7hnlz5wfs", "children": [{"type": "GList", "name": "list"}]}, {"type": "GComponent", "name": "SelfStatus_pc", "res": "ui://gytxm6z7hnlz5wfz", "children": [{"type": "GComponent", "name": "hpBar", "res": "ui://gytxm6z7hnlz5wgd", "resName": "HpBar", "package": "Fight"}, {"type": "GComponent", "name": "energyBar", "res": "ui://gytxm6z7hnlz5wgc", "resName": "EnergyBar", "package": "Fight"}, {"type": "GComponent", "name": "pow<PERSON>ar", "res": "ui://gytxm6z7hnlz5wg0", "resName": "PowBar_pc", "package": "Fight"}, {"type": "GComponent", "name": "petSkill", "res": "ui://gytxm6z7hnlz5wfq", "resName": "PetSkill_pc", "package": "Fight", "children": [{"type": "GComponent", "name": "magicBar", "res": "ui://gytxm6z7hnlz5wgb", "resName": "AngerBar", "package": "Fight"}, {"type": "GList", "name": "petSkillList"}, {"type": "GButton", "name": "arrowBtn"}]}]}, {"type": "GComponent", "name": "FirePowerUI_pc", "res": "ui://gytxm6z7hnlz5wg1", "children": [{"type": "GComponent", "name": "powerBar", "res": "ui://gytxm6z7hnlz5wff", "resName": "PowerBar_pc", "package": "Fight"}, {"type": "GComponent", "name": "shootAngle", "res": "ui://gytxm6z7hnlz5wfh", "resName": "ShootAngle_pc", "package": "Fight", "children": [{"type": "GComponent", "name": "limitAnglePanel", "res": "ui://gytxm6z7u4wm5whe", "resName": "LimitAnglePanel_pc", "package": "Fight", "children": [{"type": "GComponent", "name": "circle", "res": "ui://gytxm6z7hnlz5wfi", "resName": "Circle_pc", "package": "Fight"}, {"type": "GComponent", "name": "arrow", "res": "ui://gytxm6z7hnlz5wfj", "resName": "Arrow_pc", "package": "Fight"}]}]}]}, {"type": "GComponent", "name": "BottomInfo_pc", "res": "ui://gytxm6z7hnlz5wg6", "children": [{"type": "GComponent", "name": "rewardBoxContainer", "res": "ui://gytxm6z7krv95wd3", "resName": "EmptyCmp", "package": "Fight"}, {"type": "GComponent", "name": "firepowerUI", "res": "ui://gytxm6z7hnlz5wg1", "resName": "FirePowerUI_pc", "package": "Fight", "children": [{"type": "GComponent", "name": "powerBar", "res": "ui://gytxm6z7hnlz5wff", "resName": "PowerBar_pc", "package": "Fight"}, {"type": "GComponent", "name": "shootAngle", "res": "ui://gytxm6z7hnlz5wfh", "resName": "ShootAngle_pc", "package": "Fight", "children": [{"type": "GComponent", "name": "limitAnglePanel", "res": "ui://gytxm6z7u4wm5whe", "resName": "LimitAnglePanel_pc", "package": "Fight", "children": [{"type": "GComponent", "name": "circle", "res": "ui://gytxm6z7hnlz5wfi", "resName": "Circle_pc", "package": "Fight"}, {"type": "GComponent", "name": "arrow", "res": "ui://gytxm6z7hnlz5wfj", "resName": "Arrow_pc", "package": "Fight"}]}]}]}, {"type": "GComponent", "name": "n117", "res": "ui://gytxm6z7hnlz5wfp", "resName": "QuickSkill_Pc", "package": "Fight", "children": [{"type": "GButton", "name": "skill1"}, {"type": "GButton", "name": "skill2"}, {"type": "GButton", "name": "skill3"}]}, {"type": "GComponent", "name": "n118", "res": "ui://gytxm6z7hnlz5wfn", "resName": "FlySkill", "package": "Fight", "children": [{"type": "GButton", "name": "skill1"}, {"type": "GButton", "name": "skill2"}]}, {"type": "GComponent", "name": "selfStatus", "res": "ui://gytxm6z7hnlz5wfz", "resName": "SelfStatus_pc", "package": "Fight", "children": [{"type": "GComponent", "name": "hpBar", "res": "ui://gytxm6z7hnlz5wgd", "resName": "HpBar", "package": "Fight"}, {"type": "GComponent", "name": "energyBar", "res": "ui://gytxm6z7hnlz5wgc", "resName": "EnergyBar", "package": "Fight"}, {"type": "GComponent", "name": "pow<PERSON>ar", "res": "ui://gytxm6z7hnlz5wg0", "resName": "PowBar_pc", "package": "Fight"}, {"type": "GComponent", "name": "petSkill", "res": "ui://gytxm6z7hnlz5wfq", "resName": "PetSkill_pc", "package": "Fight", "children": [{"type": "GComponent", "name": "magicBar", "res": "ui://gytxm6z7hnlz5wgb", "resName": "AngerBar", "package": "Fight"}, {"type": "GList", "name": "petSkillList"}, {"type": "GButton", "name": "arrowBtn"}]}]}]}, {"type": "GComponent", "name": "BossThumbanail", "res": "ui://gytxm6z7i0gq4h", "children": [{"type": "GComponent", "name": "progress", "res": "ui://gytxm6z7i0gq4j", "resName": "BossHeadProgress", "package": "Fight"}]}, {"type": "GComponent", "name": "Countdown", "res": "ui://gytxm6z7igth2e", "children": [{"type": "GButton", "name": "passBtn"}]}, {"type": "GComponent", "name": "VaneUI", "res": "ui://gytxm6z7igth2g", "children": [{"type": "GComponent", "name": "effectContainer", "res": "ui://gytxm6z7krv95wd3", "resName": "EmptyCmp", "package": "Fight"}]}, {"type": "GComponent", "name": "WeaponAngle", "res": "ui://gytxm6z7js8wtjr", "children": [{"type": "GComponent", "name": "bar", "res": "ui://gytxm6z7js8wtjv", "resName": "<PERSON><PERSON><PERSON><PERSON>", "package": "Fight"}]}, {"type": "GComponent", "name": "BottomOperationUI", "res": "ui://gytxm6z7krv95wd4", "children": [{"type": "GButton", "name": "fireBtnUI"}, {"type": "GComponent", "name": "firePowerView", "res": "ui://gytxm6z7lra11h", "resName": "FirePowerUI", "package": "Fight", "children": [{"type": "GComponent", "name": "lastPowerPro", "res": "ui://gytxm6z7lra11i", "resName": "ProgressBar1", "package": "Fight"}, {"type": "GComponent", "name": "powerPro", "res": "ui://gytxm6z7lra11j", "resName": "ProgressBar2", "package": "Fight"}, {"type": "GComponent", "name": "anim", "res": "ui://gytxm6z7krv95wd3", "resName": "EmptyCmp", "package": "Fight"}]}, {"type": "GComponent", "name": "achieveUI", "res": "ui://gytxm6z7krv95wd3", "resName": "EmptyCmp", "package": "Fight"}, {"type": "GComponent", "name": "dander<PERSON>", "res": "ui://gytxm6z7fzee2o", "resName": "DanderUI", "package": "Fight", "children": [{"type": "GComponent", "name": "<PERSON><PERSON><PERSON><PERSON>", "res": "ui://gytxm6z7kt9p44", "resName": "DanderPro", "package": "Fight"}, {"type": "GButton", "name": "powBtn"}]}, {"type": "GComponent", "name": "walkUI", "res": "ui://gytxm6z7ee7s1k", "resName": "WalkUI", "package": "Fight", "children": [{"type": "GButton", "name": "touchArea"}]}, {"type": "GComponent", "name": "selfStatusUI", "res": "ui://gytxm6z7ee7s1l", "resName": "SelfStatus", "package": "Fight", "children": [{"type": "GComponent", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "res": "ui://gytxm6z79pul1e", "resName": "<PERSON><PERSON><PERSON><PERSON>", "package": "Fight"}, {"type": "GComponent", "name": "playerEnergyPro", "res": "ui://gytxm6z79pul1c", "resName": "playerEnergy", "package": "Fight"}, {"type": "GComponent", "name": "petEnergyPro", "res": "ui://gytxm6z7rwij26", "resName": "petSnergyPro", "package": "Fight"}]}, {"type": "GComponent", "name": "formulaUI", "res": "ui://gytxm6z7p1dp53", "resName": "FightFireFormulaUI", "package": "Fight", "children": [{"type": "GButton", "name": "leftBtn"}, {"type": "GButton", "name": "rightBtn"}]}, {"type": "GComponent", "name": "rewardBoxContainer", "res": "ui://gytxm6z7krv95wd3", "resName": "EmptyCmp", "package": "Fight"}, {"type": "GComponent", "name": "fireSignUI", "res": "ui://gytxm6z7au9ctc7", "resName": "FireSignUI", "package": "Fight", "children": [{"type": "GButton", "name": "firePowerTouchView"}]}]}, {"type": "GComponent", "name": "ChatPanel_pc", "res": "ui://gytxm6z7krv95wd7", "children": [{"type": "GButton", "name": "alphaBtn"}, {"type": "GButton", "name": "addFriendBtn"}, {"type": "GButton", "name": "faceBtn"}, {"type": "GButton", "name": "chatBtn"}, {"type": "GComponent", "name": "content", "res": "ui://gytxm6z7hnlz5wfg", "resName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "package": "Fight"}]}, {"type": "GComponent", "name": "MissionInfo_pc", "res": "ui://gytxm6z7l9395wgf", "children": [{"type": "GButton", "name": "n7"}]}, {"type": "GComponent", "name": "RoundRole_pc", "res": "ui://gytxm6z7l9395wgh", "children": [{"type": "GComponent", "name": "n4", "res": "ui://gytxm6z7tigk5whg", "resName": "PetHead_pc", "package": "Fight"}, {"type": "GComponent", "name": "n7", "res": "ui://gytxm6z7l9395wgi", "resName": "RoleAvatar_pc", "package": "Fight"}]}, {"type": "GComponent", "name": "TopInfo_pc", "res": "ui://gytxm6z7l9395wgj", "children": [{"type": "GComponent", "name": "smallMap", "res": "ui://gytxm6z7l9395wgo", "resName": "SmallMap_pc", "package": "Fight", "children": [{"type": "GButton", "name": "closeBtn"}, {"type": "GButton", "name": "settingBtn"}, {"type": "GButton", "name": "roundInfo"}, {"type": "GComponent", "name": "mapView", "res": "ui://gytxm6z7znm35wh9", "resName": "MapView_pc", "package": "Fight"}, {"type": "GComponent", "name": "missionInfo", "res": "ui://gytxm6z7l9395wgf", "resName": "MissionInfo_pc", "package": "Fight", "children": [{"type": "GButton", "name": "n7"}]}]}, {"type": "GComponent", "name": "vsInfo", "res": "ui://gytxm6z7l9395wgn", "resName": "VsInfoInfo_pc", "package": "Fight", "children": [{"type": "GComponent", "name": "n32", "res": "ui://gytxm6z7l9395wgk", "resName": "RoleHead_pc", "package": "Fight", "children": [{"type": "GComponent", "name": "n27", "res": "ui://gytxm6z7l9395wgl", "resName": "HpBar1", "package": "Fight"}]}, {"type": "GComponent", "name": "n33", "res": "ui://gytxm6z7l9395wgk", "resName": "RoleHead_pc", "package": "Fight", "children": [{"type": "GComponent", "name": "n27", "res": "ui://gytxm6z7l9395wgl", "resName": "HpBar1", "package": "Fight"}]}]}, {"type": "GComponent", "name": "roleInfo", "res": "ui://gytxm6z7l9395wgh", "resName": "RoundRole_pc", "package": "Fight", "children": [{"type": "GComponent", "name": "n4", "res": "ui://gytxm6z7tigk5whg", "resName": "PetHead_pc", "package": "Fight"}, {"type": "GComponent", "name": "n7", "res": "ui://gytxm6z7l9395wgi", "resName": "RoleAvatar_pc", "package": "Fight"}]}, {"type": "GComponent", "name": "countDown", "res": "ui://gytxm6z7igth2e", "resName": "Countdown", "package": "Fight", "children": [{"type": "GButton", "name": "passBtn"}]}]}, {"type": "GComponent", "name": "RoleHead_pc", "res": "ui://gytxm6z7l9395wgk", "children": [{"type": "GComponent", "name": "n27", "res": "ui://gytxm6z7l9395wgl", "resName": "HpBar1", "package": "Fight"}]}, {"type": "GComponent", "name": "VsInfoInfo_pc", "res": "ui://gytxm6z7l9395wgn", "children": [{"type": "GComponent", "name": "n32", "res": "ui://gytxm6z7l9395wgk", "resName": "RoleHead_pc", "package": "Fight", "children": [{"type": "GComponent", "name": "n27", "res": "ui://gytxm6z7l9395wgl", "resName": "HpBar1", "package": "Fight"}]}, {"type": "GComponent", "name": "n33", "res": "ui://gytxm6z7l9395wgk", "resName": "RoleHead_pc", "package": "Fight", "children": [{"type": "GComponent", "name": "n27", "res": "ui://gytxm6z7l9395wgl", "resName": "HpBar1", "package": "Fight"}]}]}, {"type": "GComponent", "name": "SmallMap_pc", "res": "ui://gytxm6z7l9395wgo", "children": [{"type": "GButton", "name": "closeBtn"}, {"type": "GButton", "name": "settingBtn"}, {"type": "GButton", "name": "roundInfo"}, {"type": "GComponent", "name": "mapView", "res": "ui://gytxm6z7znm35wh9", "resName": "MapView_pc", "package": "Fight"}, {"type": "GComponent", "name": "missionInfo", "res": "ui://gytxm6z7l9395wgf", "resName": "MissionInfo_pc", "package": "Fight", "children": [{"type": "GButton", "name": "n7"}]}]}, {"type": "GComponent", "name": "CenterInfo_pc", "res": "ui://gytxm6z7l9395wgr", "children": [{"type": "GComponent", "name": "chatView", "res": "ui://gytxm6z7krv95wd7", "resName": "ChatPanel_pc", "package": "Fight", "children": [{"type": "GButton", "name": "alphaBtn"}, {"type": "GButton", "name": "addFriendBtn"}, {"type": "GButton", "name": "faceBtn"}, {"type": "GButton", "name": "chatBtn"}, {"type": "GComponent", "name": "content", "res": "ui://gytxm6z7hnlz5wfg", "resName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "package": "Fight"}]}, {"type": "GComponent", "name": "bombSkill", "res": "ui://gytxm6z7hnlz5wfs", "resName": "BombSkill_pc", "package": "Fight", "children": [{"type": "GList", "name": "list"}]}]}, {"type": "GComponent", "name": "FirePowerUI", "res": "ui://gytxm6z7lra11h", "children": [{"type": "GComponent", "name": "lastPowerPro", "res": "ui://gytxm6z7lra11i", "resName": "ProgressBar1", "package": "Fight"}, {"type": "GComponent", "name": "powerPro", "res": "ui://gytxm6z7lra11j", "resName": "ProgressBar2", "package": "Fight"}, {"type": "GComponent", "name": "anim", "res": "ui://gytxm6z7krv95wd3", "resName": "EmptyCmp", "package": "Fight"}]}, {"type": "GComponent", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "res": "ui://gytxm6z7m3a5tkm", "children": [{"type": "GList", "name": "list"}]}, {"type": "GComponent", "name": "FightUI", "res": "ui://gytxm6z7nv385wb9", "children": [{"type": "GComponent", "name": "touchPanel", "res": "ui://gytxm6z7glb01n", "resName": "TouchLayer", "package": "Fight"}, {"type": "GComponent", "name": "bg<PERSON><PERSON><PERSON>", "res": "ui://gytxm6z7krv95wd3", "resName": "EmptyCmp", "package": "Fight"}, {"type": "GComponent", "name": "topContainer", "res": "ui://gytxm6z7krv95wd3", "resName": "EmptyCmp", "package": "Fight"}, {"type": "GComponent", "name": "bottomContainer", "res": "ui://gytxm6z7krv95wd3", "resName": "EmptyCmp", "package": "Fight"}, {"type": "GComponent", "name": "centerContainer", "res": "ui://gytxm6z7krv95wd3", "resName": "EmptyCmp", "package": "Fight"}, {"type": "GComponent", "name": "tipContainer", "res": "ui://gytxm6z7krv95wd3", "resName": "EmptyCmp", "package": "Fight"}]}, {"type": "GComponent", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "res": "ui://gytxm6z7oazc33", "children": [{"type": "GComponent", "name": "avatarBox", "res": "ui://gytxm6z7yj5g3z", "resName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "package": "Fight"}, {"type": "GComponent", "name": "progress", "res": "ui://gytxm6z7oazc35", "resName": "ProgressBar3", "package": "Fight"}, {"type": "GButton", "name": "headBtn"}]}, {"type": "GComponent", "name": "ThumbnailUI", "res": "ui://gytxm6z7oazc34", "children": [{"type": "GList", "name": "team1List"}, {"type": "GList", "name": "team2List"}, {"type": "GComponent", "name": "boss", "res": "ui://gytxm6z7i0gq4h", "resName": "BossThumbanail", "package": "Fight", "children": [{"type": "GComponent", "name": "progress", "res": "ui://gytxm6z7i0gq4j", "resName": "BossHeadProgress", "package": "Fight"}]}]}, {"type": "GComponent", "name": "FightFireFormulaUI", "res": "ui://gytxm6z7p1dp53", "children": [{"type": "GButton", "name": "leftBtn"}, {"type": "GButton", "name": "rightBtn"}]}, {"type": "GComponent", "name": "LimitAnglePanel_pc", "res": "ui://gytxm6z7u4wm5whe", "children": [{"type": "GComponent", "name": "circle", "res": "ui://gytxm6z7hnlz5wfi", "resName": "Circle_pc", "package": "Fight"}, {"type": "GComponent", "name": "arrow", "res": "ui://gytxm6z7hnlz5wfj", "resName": "Arrow_pc", "package": "Fight"}]}, {"type": "GComponent", "name": "BuffInfoUI", "res": "ui://gytxm6z7wxxx51", "children": [{"type": "GList", "name": "list"}]}, {"type": "GComponent", "name": "HeroBoxUI", "res": "ui://gytxm6z7xq3jtku", "children": [{"type": "GComponent", "name": "angle", "res": "ui://gytxm6z7xq3jtkw", "resName": "AngleComp", "package": "Fight"}]}]