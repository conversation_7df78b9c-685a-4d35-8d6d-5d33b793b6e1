{"name": "bignumber", "description": "A pure javascript implementation of BigIntegers and RSA crypto.", "engines": {"node": ">=0.4.0"}, "version": "v1.1.0", "keywords": ["bigintegers", "rsa", "pkcs"], "homepage": "http://www-cs-students.stanford.edu/~tjw/jsbn/", "author": "<PERSON> <<EMAIL>> (http://www-cs-students.stanford.edu/~tjw/)", "main": "./lib/rsa", "repository": {"type": "git", "url": "https://github.com/eschnou/node-rsa.git"}}