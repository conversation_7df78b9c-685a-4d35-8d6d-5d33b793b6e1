{"$schema": "./@types/schema/package/index.json", "package_version": 2, "name": "map-editor", "version": "1.0.0", "author": "fan.zhou", "editor": ">=3.8.6", "scripts": {"preinstall": "node ./scripts/preinstall.js", "build": "npx tsc"}, "description": "i18n:map-editor.description", "main": "./dist/main.js", "dependencies": {"fs-extra": "^10.0.0", "vue": "^3.1.4"}, "devDependencies": {"@cocos/creator-types": "^3.8.6", "@types/fs-extra": "^9.0.5", "@types/node": "^18.17.1", "typescript": "^5.8.2"}, "panels": {"default": {"title": "生成地图", "type": "dockable", "main": "dist/panels/default", "size": {"min-width": 400, "min-height": 300, "width": 1024, "height": 600}}}, "contributions": {"menu": [{"path": "Tools/map-editor", "label": "生成地图", "message": "open-panel"}], "messages": {"open-panel": {"methods": ["openPanel"]}}, "scene": {"script": "./dist/scene.js"}, "assets": {"menu": {"methods": "./dist/assets-menu.js", "createMenu": "onCreateMenu", "assetMenu": "onAssetMenu", "dbMenu": "onDBMenu", "panelMenu": "onPanelMenu"}}}}