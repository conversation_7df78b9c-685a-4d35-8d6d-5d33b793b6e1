{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "chrome",
            "request": "launch",
            "name": "CompileCocosAndLaunchChrome",
            "url": "http://localhost:7456",
            "webRoot": "${workspaceFolder}",
            "preLaunchTask": "Cocos Creator compile",
            "internalConsoleOptions": "openOnSessionStart",
            "sourceMapPathOverrides": {
                "game-base/../src/*": "${workspaceFolder}/../core/game-base/src/*",
                "game-base/../src": "${workspaceFolder}/../core/game-base/src",
                "fairygui/../src": "${workspaceFolder}/../core/FairyGUI-cocoscreator_3.x/source/src",
                "puremvc/../src": "${workspaceFolder}/../core/puremvc/src"
            }
            // "runtimeArgs": [
            //     "--disable-web-security" // 可选：解决跨域问题（按需添加）
            // ]
        },
        {
            "type": "chrome",
            "request": "launch",
            "name": "CocosLaunchChrome",
            "url": "http://localhost:7456",
            "webRoot": "${workspaceFolder}",
            "pauseForSourceMap": true,
            "internalConsoleOptions": "openOnSessionStart",
            "sourceMapPathOverrides": {
                "game-base/../src": "${workspaceFolder}/../core/game-base/src",
                "fairygui/../src": "${workspaceFolder}/../core/FairyGUI-cocoscreator_3.x/source/src",
                "puremvc/../src": "${workspaceFolder}/../core/puremvc/src"
            }
            // "runtimeArgs": [
            //     "--disable-web-security" // 可选：解决跨域问题（按需添加）
            // ]
        }
    ]
}
