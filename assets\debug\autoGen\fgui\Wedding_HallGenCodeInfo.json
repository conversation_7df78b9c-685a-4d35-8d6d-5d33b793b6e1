[{"type": "GComponent", "name": "WeddingSettingView", "res": "ui://utknvjwjdd3w12", "children": [{"type": "GComponent", "name": "bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GButton", "name": "btnConfirm"}, {"type": "GButton", "name": "btnCancel"}, {"type": "GButton", "name": "btnPassword"}, {"type": "GButton", "name": "btnAllowInvite"}, {"type": "GComponent", "name": "content", "res": "ui://utknvjwjdd3w13", "resName": "WeddingContentComp", "package": "Wedding_Hall"}, {"type": "GList", "name": "durationList"}]}, {"type": "GComponent", "name": "WeddingListTurnPage", "res": "ui://utknvjwjdd3w16", "children": [{"type": "GButton", "name": "pageFirstBtn"}, {"type": "GButton", "name": "pagePrevBtn"}, {"type": "GButton", "name": "pageNextBtn"}, {"type": "GButton", "name": "pageLastBtn"}]}, {"type": "GComponent", "name": "ProposeReceiveView", "res": "ui://utknvjwjdd3wv", "children": [{"type": "GComponent", "name": "bg", "res": "ui://dyypinptd1ui45wbv", "resName": "AlertBg3", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}, {"type": "GButton", "name": "SureBtn"}, {"type": "GButton", "name": "CancelBtn"}]}, {"type": "GComponent", "name": "applyContent", "res": "ui://utknvjwjdd3w13", "resName": "WeddingContentComp", "package": "Wedding_Hall"}, {"type": "GButton", "name": "btnInfo"}]}, {"type": "GComponent", "name": "Propose<PERSON><PERSON><PERSON><PERSON><PERSON>", "res": "ui://utknvjwjdd3wx", "children": [{"type": "GComponent", "name": "bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GButton", "name": "btnConfirm"}]}, {"type": "GComponent", "name": "ProposeAcceptView", "res": "ui://utknvjwjdd3wy", "children": [{"type": "GComponent", "name": "bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GButton", "name": "btnWedding"}, {"type": "GButton", "name": "btnCancel"}]}, {"type": "GComponent", "name": "WeddingListComp", "res": "ui://utknvjwjdd3wz", "children": [{"type": "GList", "name": "list"}, {"type": "GComponent", "name": "turnPage", "res": "ui://utknvjwjdd3w16", "resName": "WeddingListTurnPage", "package": "Wedding_Hall", "children": [{"type": "GButton", "name": "pageFirstBtn"}, {"type": "GButton", "name": "pagePrevBtn"}, {"type": "GButton", "name": "pageNextBtn"}, {"type": "GButton", "name": "pageLastBtn"}]}]}, {"type": "GComponent", "name": "WeddingHallView", "res": "ui://utknvjwjmmwul", "children": [{"type": "GButton", "name": "btnHoldWedding"}, {"type": "GButton", "name": "btnJoinWedding"}, {"type": "GButton", "name": "btnDivorce"}, {"type": "GComponent", "name": "weddingList", "res": "ui://utknvjwjdd3wz", "resName": "WeddingListComp", "package": "Wedding_Hall", "children": [{"type": "GList", "name": "list"}, {"type": "GComponent", "name": "turnPage", "res": "ui://utknvjwjdd3w16", "resName": "WeddingListTurnPage", "package": "Wedding_Hall", "children": [{"type": "GButton", "name": "pageFirstBtn"}, {"type": "GButton", "name": "pagePrevBtn"}, {"type": "GButton", "name": "pageNextBtn"}, {"type": "GButton", "name": "pageLastBtn"}]}]}]}, {"type": "GComponent", "name": "DivorceAskView", "res": "ui://utknvjwjmmwuq", "children": [{"type": "GComponent", "name": "bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GButton", "name": "btnConfirm"}, {"type": "GButton", "name": "btnCancel"}]}, {"type": "GComponent", "name": "Pro<PERSON><PERSON><PERSON><PERSON>", "res": "ui://utknvjwjmmwus", "children": [{"type": "GComponent", "name": "bg", "res": "ui://dyypinptd1ui45wbv", "resName": "AlertBg3", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}, {"type": "GButton", "name": "SureBtn"}, {"type": "GButton", "name": "CancelBtn"}]}, {"type": "GComponent", "name": "inputContent", "res": "ui://utknvjwjdd3w13", "resName": "WeddingContentComp", "package": "Wedding_Hall"}, {"type": "GButton", "name": "btnAnnounce"}]}, {"type": "GComponent", "name": "WeddingInfoView", "res": "ui://utknvjwjuit918", "children": [{"type": "GComponent", "name": "bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GButton", "name": "btnConfirm"}, {"type": "GButton", "name": "btnCancel"}, {"type": "GComponent", "name": "content", "res": "ui://utknvjwjdd3w13", "resName": "WeddingContentComp", "package": "Wedding_Hall"}]}, {"type": "GComponent", "name": "ProposeRefuseView", "res": "ui://utknvjwjuit91f", "children": [{"type": "GComponent", "name": "bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GButton", "name": "btnConfirm"}]}, {"type": "GComponent", "name": "DivorceNoticeView", "res": "ui://utknvjwjuit91g", "children": [{"type": "GComponent", "name": "bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GButton", "name": "btnClose"}]}, {"type": "GComponent", "name": "ProposeBuyRingView", "res": "ui://utknvjwjuit91h", "children": [{"type": "GComponent", "name": "bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GButton", "name": "btnConfirm"}, {"type": "GButton", "name": "btnCancel"}]}, {"type": "GComponent", "name": "WeddingInviteView", "res": "ui://utknvjwjuit91i", "children": [{"type": "GComponent", "name": "bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GButton", "name": "btnConfirm"}, {"type": "GButton", "name": "btnCancel"}]}]