[{"type": "GComponent", "name": "CurFilterMonster", "res": "ui://mciisk9yllj81c", "children": [{"type": "GComponent", "name": "list", "res": "ui://mciisk9yllj81e", "resName": "FilterMonster_popup", "package": "Room_Comm", "children": [{"type": "GList", "name": "List"}]}]}, {"type": "GComponent", "name": "FilterMonster_popup", "res": "ui://mciisk9yllj81e", "children": [{"type": "GList", "name": "List"}]}, {"type": "GComponent", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "res": "ui://mciisk9yllj81f", "children": [{"type": "GList", "name": "list"}]}, {"type": "GComponent", "name": "RoomSettingUI", "res": "ui://mciisk9yllj81k", "children": [{"type": "GComponent", "name": "Bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GComponent", "name": "RoomPattern", "res": "ui://mciisk9yllj81l", "resName": "RoomPattern", "package": "Room_Comm", "children": [{"type": "GButton", "name": "EnableInput"}]}, {"type": "GComponent", "name": "SelectMonster", "res": "ui://mciisk9yllj81m", "resName": "SelectMonstersList", "package": "Room_Comm", "children": [{"type": "GList", "name": "DesList"}, {"type": "GList", "name": "MonsterBtnsList"}, {"type": "GList", "name": "DungeonList"}]}, {"type": "GComponent", "name": "RoomDiff", "res": "ui://mciisk9yq2v13i", "resName": "RoomDifficulty", "package": "Room_Comm", "children": [{"type": "GList", "name": "List"}]}, {"type": "GButton", "name": "ConfirmBtn"}, {"type": "GButton", "name": "BossFightBtn"}]}, {"type": "GComponent", "name": "RoomPattern", "res": "ui://mciisk9yllj81l", "children": [{"type": "GButton", "name": "EnableInput"}]}, {"type": "GComponent", "name": "SelectMonstersList", "res": "ui://mciisk9yllj81m", "children": [{"type": "GList", "name": "DesList"}, {"type": "GList", "name": "MonsterBtnsList"}, {"type": "GList", "name": "DungeonList"}]}, {"type": "GComponent", "name": "UserInfoAndPlayersLv", "res": "ui://mciisk9yon3r2t", "children": [{"type": "GComponent", "name": "UserInfo", "res": "ui://mciisk9yqo2q2o", "resName": "UserInfo", "package": "Room_Comm", "children": [{"type": "GComponent", "name": "EnhanceFence", "res": "ui://mciisk9yqo2q2p", "resName": "EnhanceFence", "package": "Room_Comm", "children": [{"type": "GList", "name": "EnhanceList"}]}, {"type": "GList", "name": "EnhanceList_Pve"}]}, {"type": "GComponent", "name": "PlayersList", "res": "ui://mciisk9yqo2q2m", "resName": "PlayersList", "package": "Room_Comm", "children": [{"type": "GList", "name": "PlayersList"}]}]}, {"type": "GComponent", "name": "PlayerCardBtn", "res": "ui://mciisk9yon3r2w", "children": [{"type": "GButton", "name": "KickOutBtn"}]}, {"type": "GComponent", "name": "RoomCardItem", "res": "ui://mciisk9yon3r2x", "children": [{"type": "GButton", "name": "PlayerBtn"}, {"type": "GButton", "name": "StanceBtn"}]}, {"type": "GComponent", "name": "InRoomFreeGuild", "res": "ui://mciisk9yon3r2y", "children": [{"type": "GList", "name": "PlayersList"}]}, {"type": "GComponent", "name": "RoomInfoBtn", "res": "ui://mciisk9yon3r31", "children": [{"type": "GButton", "name": "SetRoomInfoBtn"}]}, {"type": "GComponent", "name": "BattleModeComp", "res": "ui://mciisk9yon3r32", "children": [{"type": "GButton", "name": "RoomInfoBtn"}, {"type": "GComponent", "name": "BattleMode", "res": "ui://mciisk9yon3r33", "resName": "BattleMode", "package": "Room_Comm", "children": [{"type": "GButton", "name": "FreeBattleBtn"}, {"type": "GButton", "name": "GuildBattleBtn"}]}, {"type": "GComponent", "name": "SelectMonsters", "res": "ui://mciisk9yq2v13q", "resName": "SelectMonstersComp", "package": "Room_Comm", "children": [{"type": "GButton", "name": "SelectBtn"}, {"type": "GButton", "name": "ReSelectBtn"}, {"type": "GComponent", "name": "Loot", "res": "ui://mciisk9yq2v13t", "resName": "LootComp", "package": "Room_Comm", "children": [{"type": "GList", "name": "LootsList"}, {"type": "GButton", "name": "LootListOpenBtn"}]}]}]}, {"type": "GComponent", "name": "BattleMode", "res": "ui://mciisk9yon3r33", "children": [{"type": "GButton", "name": "FreeBattleBtn"}, {"type": "GButton", "name": "GuildBattleBtn"}]}, {"type": "GComponent", "name": "WatchBattleBtn", "res": "ui://mciisk9yon3r34", "children": [{"type": "GButton", "name": "WatchBattlePlayerBtn"}]}, {"type": "GComponent", "name": "WatchBattlePlayerBtn", "res": "ui://mciisk9yon3r35", "children": [{"type": "GButton", "name": "kickOutBtn"}]}, {"type": "GComponent", "name": "WatchBattleComp", "res": "ui://mciisk9yon3r36", "children": [{"type": "GButton", "name": "WatchBattleBtn"}, {"type": "GButton", "name": "PlayerBtn"}, {"type": "GButton", "name": "PlayerBtn_2"}]}, {"type": "GComponent", "name": "InRoomChallenge", "res": "ui://mciisk9yon3r37", "children": [{"type": "GList", "name": "BlueList"}, {"type": "GList", "name": "RedList"}, {"type": "GButton", "name": "RoomInfoBtn"}]}, {"type": "GComponent", "name": "WatchBattleBtn_Small", "res": "ui://mciisk9yon3r38", "children": [{"type": "GButton", "name": "PlayerBtn"}]}, {"type": "GComponent", "name": "WatchBattlePlayerBtn_Small", "res": "ui://mciisk9yon3r39", "children": [{"type": "GButton", "name": "kickOutBtn"}]}, {"type": "GComponent", "name": "PropsComp", "res": "ui://mciisk9yon3r3a", "children": [{"type": "GList", "name": "CarryPropsList"}, {"type": "GList", "name": "BattlePropsList"}]}, {"type": "GComponent", "name": "InRoomUI", "res": "ui://mciisk9yon3r3e", "children": [{"type": "GComponent", "name": "InRoomFreeGuild", "res": "ui://mciisk9yon3r2y", "resName": "InRoomFreeGuild", "package": "Room_Comm", "children": [{"type": "GList", "name": "PlayersList"}]}, {"type": "GComponent", "name": "InRoomChallenge", "res": "ui://mciisk9yon3r37", "resName": "InRoomChallenge", "package": "Room_Comm", "children": [{"type": "GList", "name": "BlueList"}, {"type": "GList", "name": "RedList"}, {"type": "GButton", "name": "RoomInfoBtn"}]}, {"type": "GComponent", "name": "BattleMode", "res": "ui://mciisk9yon3r32", "resName": "BattleModeComp", "package": "Room_Comm", "children": [{"type": "GButton", "name": "RoomInfoBtn"}, {"type": "GComponent", "name": "BattleMode", "res": "ui://mciisk9yon3r33", "resName": "BattleMode", "package": "Room_Comm", "children": [{"type": "GButton", "name": "FreeBattleBtn"}, {"type": "GButton", "name": "GuildBattleBtn"}]}, {"type": "GComponent", "name": "SelectMonsters", "res": "ui://mciisk9yq2v13q", "resName": "SelectMonstersComp", "package": "Room_Comm", "children": [{"type": "GButton", "name": "SelectBtn"}, {"type": "GButton", "name": "ReSelectBtn"}, {"type": "GComponent", "name": "Loot", "res": "ui://mciisk9yq2v13t", "resName": "LootComp", "package": "Room_Comm", "children": [{"type": "GList", "name": "LootsList"}, {"type": "GButton", "name": "LootListOpenBtn"}]}]}]}, {"type": "GComponent", "name": "Props", "res": "ui://mciisk9yon3r3a", "resName": "PropsComp", "package": "Room_Comm", "children": [{"type": "GList", "name": "CarryPropsList"}, {"type": "GList", "name": "BattlePropsList"}]}, {"type": "GComponent", "name": "WatchBattle", "res": "ui://mciisk9yon3r36", "resName": "WatchBattleComp", "package": "Room_Comm", "children": [{"type": "GButton", "name": "WatchBattleBtn"}, {"type": "GButton", "name": "PlayerBtn"}, {"type": "GButton", "name": "PlayerBtn_2"}]}, {"type": "GButton", "name": "InviteBtn"}, {"type": "GButton", "name": "StartGameBtn"}, {"type": "GButton", "name": "CrossRealmBtn"}, {"type": "GButton", "name": "ChangeTeamBtn"}]}, {"type": "GComponent", "name": "RoomDifficulty", "res": "ui://mciisk9yq2v13i", "children": [{"type": "GList", "name": "List"}]}, {"type": "GComponent", "name": "SelectMonstersComp", "res": "ui://mciisk9yq2v13q", "children": [{"type": "GButton", "name": "SelectBtn"}, {"type": "GButton", "name": "ReSelectBtn"}, {"type": "GComponent", "name": "Loot", "res": "ui://mciisk9yq2v13t", "resName": "LootComp", "package": "Room_Comm", "children": [{"type": "GList", "name": "LootsList"}, {"type": "GButton", "name": "LootListOpenBtn"}]}]}, {"type": "GComponent", "name": "LootComp", "res": "ui://mciisk9yq2v13t", "children": [{"type": "GList", "name": "LootsList"}, {"type": "GButton", "name": "LootListOpenBtn"}]}, {"type": "GComponent", "name": "FindRoomUI", "res": "ui://mciisk9yq2v142", "children": [{"type": "GComponent", "name": "Bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GButton", "name": "InputEnable"}, {"type": "GButton", "name": "ConfirmBtn"}, {"type": "GButton", "name": "CancelBtn"}]}, {"type": "GComponent", "name": "SetRoomPasswordUI", "res": "ui://mciisk9yq2v143", "children": [{"type": "GComponent", "name": "Bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GButton", "name": "ConfirmBtn"}, {"type": "GButton", "name": "CancelBtn"}, {"type": "GButton", "name": "EnableInput"}]}, {"type": "GComponent", "name": "InviteUI", "res": "ui://mciisk9yq2v144", "children": [{"type": "GComponent", "name": "Bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GButton", "name": "RefreshListBtn"}, {"type": "GButton", "name": "ShortcutBtn"}, {"type": "GList", "name": "CateList"}, {"type": "GList", "name": "PlayersList"}]}, {"type": "GComponent", "name": "PlayerInviteItem", "res": "ui://mciisk9yq2v146", "children": [{"type": "GButton", "name": "InviteBtn"}]}, {"type": "GComponent", "name": "VerificationCodeUI", "res": "ui://mciisk9yq2v149", "children": [{"type": "GComponent", "name": "Bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GButton", "name": "ConfirmBtn"}, {"type": "GButton", "name": "CancelBtn"}, {"type": "GButton", "name": "ZeroBtn"}, {"type": "GButton", "name": "OneBtn"}, {"type": "GButton", "name": "TwoBtn"}, {"type": "GButton", "name": "ThreeBtn"}, {"type": "GButton", "name": "FourBtn"}, {"type": "GButton", "name": "FiveBtn"}, {"type": "GButton", "name": "SixBtn"}, {"type": "GButton", "name": "SevenBtn"}, {"type": "GButton", "name": "EightBtn"}, {"type": "GButton", "name": "NineBtn"}]}, {"type": "GComponent", "name": "RoomListUI", "res": "ui://mciisk9yqo2q23", "children": [{"type": "GComponent", "name": "LobbyIcon", "res": "ui://mciisk9yqo2q24", "resName": "LobbyIcon", "package": "Room_Comm"}, {"type": "GComponent", "name": "RoomList", "res": "ui://mciisk9yqo2q25", "resName": "RoomList", "package": "Room_Comm", "children": [{"type": "GList", "name": "RoomList"}, {"type": "GComponent", "name": "TipsInfo", "res": "ui://mciisk9yqo2q2a", "resName": "TipsInfo", "package": "Room_Comm", "children": [{"type": "GComponent", "name": "ContentTips", "res": "ui://mciisk9yqo2q27", "resName": "ContentTips", "package": "Room_Comm"}]}, {"type": "GButton", "name": "PrePageBtn"}, {"type": "GButton", "name": "NextPageBtn"}, {"type": "GList", "name": "RoomList_Pve"}]}, {"type": "GComponent", "name": "Words", "res": "ui://mciisk9yllj81b", "resName": "RoomListWords", "package": "Room_Comm"}, {"type": "GButton", "name": "GamePattern"}, {"type": "GComponent", "name": "Curchannel", "res": "ui://mciisk9yqo2q2j", "resName": "CurChannel", "package": "Room_Comm", "children": [{"type": "GButton", "name": "CurChannelBtn"}]}, {"type": "GComponent", "name": "PlayersInfo", "res": "ui://mciisk9yon3r2t", "resName": "UserInfoAndPlayersLv", "package": "Room_Comm", "children": [{"type": "GComponent", "name": "UserInfo", "res": "ui://mciisk9yqo2q2o", "resName": "UserInfo", "package": "Room_Comm", "children": [{"type": "GComponent", "name": "EnhanceFence", "res": "ui://mciisk9yqo2q2p", "resName": "EnhanceFence", "package": "Room_Comm", "children": [{"type": "GList", "name": "EnhanceList"}]}, {"type": "GList", "name": "EnhanceList_Pve"}]}, {"type": "GComponent", "name": "PlayersList", "res": "ui://mciisk9yqo2q2m", "resName": "PlayersList", "package": "Room_Comm", "children": [{"type": "GList", "name": "PlayersList"}]}]}, {"type": "GComponent", "name": "StartSearch", "res": "ui://mciisk9yqo2q2k", "resName": "StartComp", "package": "Room_Comm", "children": [{"type": "GButton", "name": "TeamBattleBtn"}, {"type": "GButton", "name": "SearchBtn"}, {"type": "GButton", "name": "StartGameBtn"}]}, {"type": "GButton", "name": "FilterMonster"}, {"type": "GButton", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"type": "GButton", "name": "ResetBtn"}]}, {"type": "GComponent", "name": "RoomList", "res": "ui://mciisk9yqo2q25", "children": [{"type": "GList", "name": "RoomList"}, {"type": "GComponent", "name": "TipsInfo", "res": "ui://mciisk9yqo2q2a", "resName": "TipsInfo", "package": "Room_Comm", "children": [{"type": "GComponent", "name": "ContentTips", "res": "ui://mciisk9yqo2q27", "resName": "ContentTips", "package": "Room_Comm"}]}, {"type": "GButton", "name": "PrePageBtn"}, {"type": "GButton", "name": "NextPageBtn"}, {"type": "GList", "name": "RoomList_Pve"}]}, {"type": "GComponent", "name": "TipsInfo", "res": "ui://mciisk9yqo2q2a", "children": [{"type": "GComponent", "name": "ContentTips", "res": "ui://mciisk9yqo2q27", "resName": "ContentTips", "package": "Room_Comm"}]}, {"type": "GComponent", "name": "ComboBox_popup", "res": "ui://mciisk9yqo2q2g", "children": [{"type": "GList", "name": "list"}]}, {"type": "GComponent", "name": "ComboBox", "res": "ui://mciisk9yqo2q2i", "children": [{"type": "GComponent", "name": "PatternList", "res": "ui://mciisk9yqo2q2g", "resName": "ComboBox_popup", "package": "Room_Comm", "children": [{"type": "GList", "name": "list"}]}]}, {"type": "GComponent", "name": "CurChannel", "res": "ui://mciisk9yqo2q2j", "children": [{"type": "GButton", "name": "CurChannelBtn"}]}, {"type": "GComponent", "name": "StartComp", "res": "ui://mciisk9yqo2q2k", "children": [{"type": "GButton", "name": "TeamBattleBtn"}, {"type": "GButton", "name": "SearchBtn"}, {"type": "GButton", "name": "StartGameBtn"}]}, {"type": "GComponent", "name": "PlayersList", "res": "ui://mciisk9yqo2q2m", "children": [{"type": "GList", "name": "PlayersList"}]}, {"type": "GComponent", "name": "UserInfo", "res": "ui://mciisk9yqo2q2o", "children": [{"type": "GComponent", "name": "EnhanceFence", "res": "ui://mciisk9yqo2q2p", "resName": "EnhanceFence", "package": "Room_Comm", "children": [{"type": "GList", "name": "EnhanceList"}]}, {"type": "GList", "name": "EnhanceList_Pve"}]}, {"type": "GComponent", "name": "EnhanceFence", "res": "ui://mciisk9yqo2q2p", "children": [{"type": "GList", "name": "EnhanceList"}]}, {"type": "GComponent", "name": "RoomPattern_Pvp", "res": "ui://mciisk9ystik4b", "children": [{"type": "GButton", "name": "EnableInput"}, {"type": "GList", "name": "List"}]}, {"type": "GComponent", "name": "RoomSettingPvpUI", "res": "ui://mciisk9ystik4d", "children": [{"type": "GComponent", "name": "Bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GComponent", "name": "RoomPattern", "res": "ui://mciisk9ystik4b", "resName": "RoomPattern_Pvp", "package": "Room_Comm", "children": [{"type": "GButton", "name": "EnableInput"}, {"type": "GList", "name": "List"}]}, {"type": "GComponent", "name": "SelectMapListComp", "res": "ui://mciisk9ystik4e", "resName": "SelectMapList", "package": "Room_Comm", "children": [{"type": "GList", "name": "DesList"}, {"type": "GList", "name": "MapList"}]}, {"type": "GButton", "name": "ConfirmBtn"}]}, {"type": "GComponent", "name": "SelectMapList", "res": "ui://mciisk9ystik4e", "children": [{"type": "GList", "name": "DesList"}, {"type": "GList", "name": "MapList"}]}]