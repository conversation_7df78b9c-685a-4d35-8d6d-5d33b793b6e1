System.register(["__unresolved_0", "cc", "core", "game-base", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Tween, tween, cfg, LKey, GBaseModel, LangMgr, ModelDecorator, TipDirection, TipInfo, UITipsManager, CommonTextTip, _dec, _class, _class2, _crd, RoomModel, Entrance, RoomType;

  function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

  function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return typeof key === "symbol" ? key : String(key); }

  function _toPrimitive(input, hint) { if (typeof input !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (typeof res !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }

  function _reportPossibleCrUseOfPve_Info(extras) {
    _reporterNs.report("Pve_Info", "auto/tableCodes/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfShop_Goods(extras) {
    _reporterNs.report("Shop_Goods", "auto/tableCodes/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfcfg(extras) {
    _reporterNs.report("cfg", "core", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLKey(extras) {
    _reporterNs.report("LKey", "core", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGButton(extras) {
    _reporterNs.report("GButton", "fairygui-cc", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGTextField(extras) {
    _reporterNs.report("GTextField", "fairygui-cc", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGBaseModel(extras) {
    _reporterNs.report("GBaseModel", "game-base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLangMgr(extras) {
    _reporterNs.report("LangMgr", "game-base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfModelDecorator(extras) {
    _reporterNs.report("ModelDecorator", "game-base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTipBase(extras) {
    _reporterNs.report("TipBase", "game-base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTipDirection(extras) {
    _reporterNs.report("TipDirection", "game-base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTipInfo(extras) {
    _reporterNs.report("TipInfo", "game-base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUITipsManager(extras) {
    _reporterNs.report("UITipsManager", "game-base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfCommonTextTip(extras) {
    _reporterNs.report("CommonTextTip", "../../comm/view/tips/CommonTextTip", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Tween = _cc.Tween;
      tween = _cc.tween;
    }, function (_core) {
      cfg = _core.cfg;
      LKey = _core.LKey;
    }, function (_gameBase) {
      GBaseModel = _gameBase.GBaseModel;
      LangMgr = _gameBase.LangMgr;
      ModelDecorator = _gameBase.ModelDecorator;
      TipDirection = _gameBase.TipDirection;
      TipInfo = _gameBase.TipInfo;
      UITipsManager = _gameBase.UITipsManager;
    }, function (_unresolved_2) {
      CommonTextTip = _unresolved_2.CommonTextTip;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "99347fZDTtAponTJoopFqP+", "RoomModel", undefined);

      __checkObsolete__(['Tween', 'tween']);

      _export("RoomModel", RoomModel = (_dec = (_crd && ModelDecorator === void 0 ? (_reportPossibleCrUseOfModelDecorator({
        error: Error()
      }), ModelDecorator) : ModelDecorator)(), _dec(_class = (_class2 = class RoomModel extends (_crd && GBaseModel === void 0 ? (_reportPossibleCrUseOfGBaseModel({
        error: Error()
      }), GBaseModel) : GBaseModel) {
        constructor() {
          super(...arguments);

          _defineProperty(this, "tipBase", void 0);

          _defineProperty(this, "DOUBLEEXPCARD", 11998);

          _defineProperty(this, "DOUBLEMERITCARD", 11997);

          _defineProperty(this, "PREVENTKICKCARD", 11996);

          _defineProperty(this, "indexNums", []);

          _defineProperty(this, "currentIndex", 0);

          _defineProperty(this, "MAX_ROOM_COUNT", 8);

          _defineProperty(this, "takeAlongPropsArray", [0, 0, 0]);

          _defineProperty(this, "PVP_TIPS_TWEEN_TAG", 10000);

          _defineProperty(this, "PVE_TIPS_TWEEN_TAG", 10001);
        }

        //public static readonly NO_SUITABLE_ROOMS: string = "暂时没有合适的房间，请稍后再试。";
        //
        getPropBean(id) {
          var propBean = (_crd && cfg === void 0 ? (_reportPossibleCrUseOfcfg({
            error: Error()
          }), cfg) : cfg).tables.Shop_GoodsTable.get(id);

          if (propBean !== null) {
            return propBean;
          }

          return null;
        }
        /**
         * 获取pve房间信息
         * @param id 房间id
         * @returns 房间信息
         */


        getPveRoomInfo(id) {
          //id与map文件夹名一致
          var bean = (_crd && cfg === void 0 ? (_reportPossibleCrUseOfcfg({
            error: Error()
          }), cfg) : cfg).tables.Pve_InfoTable.get(id);

          if (bean !== null) {
            return bean;
          }

          return null;
        }
        /**
         * 获取pve等级限制条件
         * @param field 等级限制字段
         * @returns 等级限制数组
         * @example 1-10|11-20|21-30 => [1-10, 11-20, 21-30]
         */


        getPveLvLimitArray(field) {
          if (field == null || field == undefined) return [];
          return field.split("|");
        }
        /**
         * 获取pve等级限制的左边界
         * @param array 等级限制数组
         * @returns 左边界数组
         * @example [1-10, 11-20, 21-30] => [1, 11, 21]
         */


        getPveLvLimitNumberArray(array) {
          if (array.length == 0) return [];
          return array.map(section => {
            var leftPart = section.split('-')[0];
            return Number(leftPart);
          });
        }
        /**
         * 文本换行
         * @param input 输入文本
         * @param maxPerLine 每行最大字符数
         * @returns 多少个字符换行后的文本
         * @example "1234567890" => "123456\n7890"
         */


        wrapText(input) {
          var maxPerLine = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 12;
          if (!input) return "";
          var result = [];
          var count = 0;

          for (var c of input) {
            result.push(c);
            count++;

            if (count % maxPerLine === 0) {
              result.push("\n");
            }
          }

          return result.join("").replace(/\n$/, "");
        }

        getSkillId(index) {
          if (index == 0) {
            return this.DOUBLEEXPCARD;
          } else if (index == 1) {
            return this.DOUBLEMERITCARD;
          } else if (index == 2) {
            return this.PREVENTKICKCARD;
          } else {
            //配表中没找到，后续添加
            return 0;
          }
        }

        resetNumbers() {
          this.indexNums = [];

          for (var i = 1; i <= 41; i++) {
            this.indexNums.push(i);
          }

          for (var _i = this.indexNums.length - 1; _i > 0; _i--) {
            var j = Math.floor(Math.random() * (_i + 1));
            [this.indexNums[_i], this.indexNums[j]] = [this.indexNums[j], this.indexNums[_i]];
          }

          this.currentIndex = 0;
        }

        getNextRandomNumber() {
          if (this.currentIndex >= this.indexNums.length) {
            this.resetNumbers();
          }

          var num = this.indexNums[this.currentIndex];
          this.currentIndex++;
          return num;
        }

        getFriendshipTips() {
          var id = this.getNextRandomNumber();
          var bean = (_crd && cfg === void 0 ? (_reportPossibleCrUseOfcfg({
            error: Error()
          }), cfg) : cfg).tables.MovingNotificationTable.get(id);

          if (bean !== null) {
            return (_crd && LangMgr === void 0 ? (_reportPossibleCrUseOfLangMgr({
              error: Error()
            }), LangMgr) : LangMgr).inst().getText(bean.TipsContent);
          }

          return "";
        }

        friendshipTipsTween(content, height, tag) {
          if (content == null || content == undefined) {
            return;
          }

          content.setPosition(content.x, height);
          content.text = this.getFriendshipTips();
          var t1 = tween(content).to(1.0, {
            y: content.y - height
          }, {
            easing: "linear"
          }).delay(10);
          var t2 = tween(content).to(1.0, {
            y: -height
          }, {
            easing: "linear"
          }).call(() => {
            content.setPosition(content.x, height);
            content.text = this.getFriendshipTips();
          });
          tween(content).tag(tag).sequence(t1, t2).repeatForever().start();
        }

        stopTween(tag) {
          Tween.stopAllByTag(tag);
        }

        showTextTip(btn, content) {
          var tipDir = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (_crd && TipDirection === void 0 ? (_reportPossibleCrUseOfTipDirection({
            error: Error()
          }), TipDirection) : TipDirection).TOP_CENTER;
          if (btn == null || btn == undefined) return;

          if (this.tipBase != null) {
            this.tipBase.visibility = true;
          }

          var tipInfo = new (_crd && TipInfo === void 0 ? (_reportPossibleCrUseOfTipInfo({
            error: Error()
          }), TipInfo) : TipInfo)();
          tipInfo.uiName = _crd && CommonTextTip === void 0 ? (_reportPossibleCrUseOfCommonTextTip({
            error: Error()
          }), CommonTextTip) : CommonTextTip;
          tipInfo.target = btn;
          tipInfo.tipsDirections = [tipDir];
          tipInfo.params = content;
          (_crd && UITipsManager === void 0 ? (_reportPossibleCrUseOfUITipsManager({
            error: Error()
          }), UITipsManager) : UITipsManager).inst().showTip(tipInfo).then(uiTips => {
            this.tipBase = uiTips;
          });
        }

        HideTextTip() {
          if (this.tipBase != null) {
            this.tipBase.visibility = false;
          }
        } //C_tank_view_common_RoomIIPropTip_Energy


        getTextFormat(name, des, power) {
          var temp = "[b][color=#FFFF99]".concat(name, "[/b][/color]") + "\n" + "[b]".concat((_crd && LangMgr === void 0 ? (_reportPossibleCrUseOfLangMgr({
            error: Error()
          }), LangMgr) : LangMgr).inst().getText((_crd && LKey === void 0 ? (_reportPossibleCrUseOfLKey({
            error: Error()
          }), LKey) : LKey).C_ddt_pets_skillTipLost), "[/b]: ") + "[color=#F2C834]".concat(power, "[/color]") + (_crd && LangMgr === void 0 ? (_reportPossibleCrUseOfLangMgr({
            error: Error()
          }), LangMgr) : LangMgr).inst().getText((_crd && LKey === void 0 ? (_reportPossibleCrUseOfLKey({
            error: Error()
          }), LKey) : LKey).C_energy) + "\n" + "[b]".concat((_crd && LangMgr === void 0 ? (_reportPossibleCrUseOfLangMgr({
            error: Error()
          }), LangMgr) : LangMgr).inst().getText((_crd && LKey === void 0 ? (_reportPossibleCrUseOfLKey({
            error: Error()
          }), LKey) : LKey).C_ddt_pets_skillTipDesc), "[/b]: ") + "\n" + des;
          return "[size=15]".concat(temp, "[/size]");
        }

        onRegister() {
          this.resetNumbers();
        }

        onRemove() {}

      }, _defineProperty(_class2, "className", "RoomModel"), _class2)) || _class));

      _export("Entrance", Entrance = /*#__PURE__*/function (Entrance) {
        Entrance[Entrance["ENTER"] = 0] = "ENTER";
        Entrance[Entrance["FIND"] = 1] = "FIND";
        return Entrance;
      }({}));

      _export("RoomType", RoomType = /*#__PURE__*/function (RoomType) {
        RoomType[RoomType["PVP"] = 0] = "PVP";
        RoomType[RoomType["PVE"] = 1] = "PVE";
        RoomType[RoomType["CHALLENGE"] = 2] = "CHALLENGE";
        RoomType[RoomType["ELITE"] = 3] = "ELITE";
        return RoomType;
      }({}));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=8dccbaf37772f984907914f73b33da9fd0451bec.js.map