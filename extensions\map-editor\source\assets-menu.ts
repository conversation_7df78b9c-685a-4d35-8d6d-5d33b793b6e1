import packageJSON from '../package.json';

export function onAssetMenu(assetInfo: any) {
return [
    {
    label: 'Map Editor',
    submenu: [
        {
        label: '导出弹坑数据',
        // enabled: assetInfo.isDirectory,
        async click() {

            if(assetInfo.isDirectory){
                if(assetInfo.url.indexOf('crater') === -1){
                    console.error('请选择crater文件夹或者crater文件夹下的子文件夹');
                    return;
                }
                let result = await Editor.Message.request('asset-db', 'query-assets', { pattern: `${assetInfo.url}/**/*` ,ccType: 'cc.SpriteFrame' });
                for (let i = 0; i < result.length; i++) {
                    const element = result[i];
                    if(element.displayName == 'crater'){
                        await Editor.Message.request('scene', 'execute-scene-script', {
                            name : packageJSON.name,
                            method : 'exportCraterData',
                            args : [element.url]
                        });
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }
                }

            }else if(assetInfo.type == 'cc.SpriteFrame' ){
                await Editor.Message.request('scene', 'execute-scene-script', {
                    name : packageJSON.name,
                    method : 'exportCraterData',
                    args : [assetInfo.url]
                });
            }else{
                console.error('请选择crater目录下的SpriteFrame文件');
                return;
            }
            console.log('导出地形数据完成');

            
            await new Promise(resolve => setTimeout(resolve, 100));

        },
        },
        {
        label: '导出地形数据',
        // enabled: assetInfo.isDirectory,
        async click() {
            if(assetInfo.isDirectory){
                if(assetInfo.url.indexOf('map') === -1){
                    console.error('请选择map文件夹或者map文件夹下的子文件夹');
                    return;
                }
                let result = await Editor.Message.request('asset-db', 'query-assets', { pattern: `${assetInfo.url}/**/*` ,ccType: 'cc.Prefab' });
                for (let i = 0; i < result.length; i++) {
                    const element = result[i];
                    await Editor.Message.request('scene', 'execute-scene-script', {
                        name : packageJSON.name,
                        method : 'exportTerrainData',
                        args : [element.url]
                    });
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

            }else if(assetInfo.type == 'cc.Prefab' ){
                const result = await Editor.Message.request('scene', 'execute-scene-script', {
                    name : packageJSON.name,
                    method : 'exportTerrainData',
                    args : [assetInfo.url]
                });
            }else{
                console.error('请选择地图的Prefab文件');
                return;
            }
            console.log('导出地形数据完成');
        },
        },
        {
            label: '创建地图Prefab',
            enabled: assetInfo.isDirectory,
            async click() {
                if(assetInfo.name == "map"){
                    let result = await Editor.Message.request('asset-db', 'query-assets', { pattern: `${assetInfo.url}/*` ,ccType: 'cc.Asset' });
                    for (let i = 0; i < result.length; i++) {
                        const element = result[i];
                        if(element.isDirectory){
                            // console.log(`创建地图：${element.url}`)
                            const result = await Editor.Message.request('scene', 'execute-scene-script', {
                                name : packageJSON.name,
                                method : 'createMapPrefab',
                                args : [element.url]
                            });
                            await new Promise(resolve => setTimeout(resolve, 100));
                        }
                    }
                }else{
                    const result = await Editor.Message.request('scene', 'execute-scene-script', {
                        name : packageJSON.name,
                        method : 'createMapPrefab',
                        args : [assetInfo.url]
                    });
                }
                console.log('创建地图Prefab完成');
            },
            },
    ],
    },
];
};