[{"type": "GComponent", "name": "GiftBoxChildPage", "res": "ui://dqpwuystx0gj10", "children": [{"type": "GList", "name": "GiftBoxList"}]}, {"type": "GComponent", "name": "GiftBoxPage", "res": "ui://dqpwuystx0gjl", "children": [{"type": "GComponent", "name": "TurnPage", "res": "ui://dqpwuystx0gjq", "resName": "TurnPage", "package": "Bag_GiftBox", "children": [{"type": "GButton", "name": "pagePrevBtn"}, {"type": "GButton", "name": "pageNextBtn"}]}, {"type": "GComponent", "name": "CharmProgress", "res": "ui://dqpwuystx0gju", "resName": "CharmProgressBar", "package": "Bag_GiftBox"}, {"type": "GList", "name": "GiftGoodsList"}, {"type": "GList", "name": "CollectTabList"}, {"type": "GList", "name": "TabList"}, {"type": "GComponent", "name": "GiftBoxChildPage", "res": "ui://dqpwuystx0gj10", "resName": "GiftBoxChildPage", "package": "Bag_GiftBox", "children": [{"type": "GList", "name": "GiftBoxList"}]}, {"type": "GComponent", "name": "GiftRecordChildPage", "res": "ui://dqpwuystx0gjx", "resName": "GiftRecordChildPage", "package": "Bag_GiftBox", "children": [{"type": "GComponent", "name": "n7", "res": "ui://dqpwuystx0gjw", "resName": "GivenNone", "package": "Bag_GiftBox"}, {"type": "GList", "name": "RecordTabList"}, {"type": "GList", "name": "RecordLogList"}]}]}, {"type": "GComponent", "name": "GoodsItem", "res": "ui://dqpwuystx0gjm", "children": [{"type": "GButton", "name": "DonateBtn"}]}, {"type": "GComponent", "name": "TurnPage", "res": "ui://dqpwuystx0gjq", "children": [{"type": "GButton", "name": "pagePrevBtn"}, {"type": "GButton", "name": "pageNextBtn"}]}, {"type": "GComponent", "name": "GiftRecordChildPage", "res": "ui://dqpwuystx0gjx", "children": [{"type": "GComponent", "name": "n7", "res": "ui://dqpwuystx0gjw", "resName": "GivenNone", "package": "Bag_GiftBox"}, {"type": "GList", "name": "RecordTabList"}, {"type": "GList", "name": "RecordLogList"}]}]