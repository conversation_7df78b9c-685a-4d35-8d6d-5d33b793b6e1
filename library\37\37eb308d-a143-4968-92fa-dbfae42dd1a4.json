{"__type__": "cc.Json<PERSON>set", "_name": "soundtable", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "json": [{"Id": 1, "Path": "effect/07ty032", "SoundType": 2, "IsLoop": false}, {"Id": 2, "Path": "effect/08yq068", "SoundType": 2, "IsLoop": false}, {"Id": 3, "Path": "effect/13", "SoundType": 2, "IsLoop": false}, {"Id": 4, "Path": "effect/15", "SoundType": 2, "IsLoop": false}, {"Id": 5, "Path": "effect/16", "SoundType": 2, "IsLoop": false}, {"Id": 6, "Path": "effect/17", "SoundType": 2, "IsLoop": false}, {"Id": 7, "Path": "effect/20", "SoundType": 2, "IsLoop": false}, {"Id": 8, "Path": "effect/21", "SoundType": 2, "IsLoop": false}, {"Id": 9, "Path": "effect/22", "SoundType": 2, "IsLoop": false}, {"Id": 10, "Path": "effect/225", "SoundType": 2, "IsLoop": false}, {"Id": 11, "Path": "effect/226", "SoundType": 2, "IsLoop": false}, {"Id": 12, "Path": "effect/227", "SoundType": 2, "IsLoop": false}, {"Id": 13, "Path": "effect/228", "SoundType": 2, "IsLoop": false}, {"Id": 14, "Path": "effect/23", "SoundType": 2, "IsLoop": false}, {"Id": 15, "Path": "effect/24", "SoundType": 2, "IsLoop": false}, {"Id": 16, "Path": "effect/5-0", "SoundType": 2, "IsLoop": false}, {"Id": 17, "Path": "effect/Bow-发射", "SoundType": 2, "IsLoop": false}, {"Id": 18, "Path": "effect/bow必杀-发射", "SoundType": 2, "IsLoop": false}, {"Id": 19, "Path": "effect/Cannon-发射", "SoundType": 2, "IsLoop": false}, {"Id": 20, "Path": "effect/cannon必杀-发射", "SoundType": 2, "IsLoop": false}, {"Id": 21, "Path": "effect/Dynamite-发射", "SoundType": 2, "IsLoop": false}, {"Id": 22, "Path": "effect/dynamite必杀-发射", "SoundType": 2, "IsLoop": false}, {"Id": 23, "Path": "effect/Fan-发射", "SoundType": 2, "IsLoop": false}, {"Id": 24, "Path": "effect/fan必杀-发射", "SoundType": 2, "IsLoop": false}, {"Id": 25, "Path": "effect/fiyingdagger必杀-发射", "SoundType": 2, "IsLoop": false}, {"Id": 26, "Path": "effect/Flyingdagger-发射", "SoundType": 2, "IsLoop": false}, {"Id": 27, "Path": "effect/Gun-发射", "SoundType": 2, "IsLoop": false}, {"Id": 28, "Path": "effect/gun必杀-发射", "SoundType": 2, "IsLoop": false}, {"Id": 29, "Path": "effect/Hammer-发射", "SoundType": 2, "IsLoop": false}, {"Id": 30, "Path": "effect/hammer必杀-发射", "SoundType": 2, "IsLoop": false}, {"Id": 31, "Path": "effect/Thunder-发射", "SoundType": 2, "IsLoop": false}, {"Id": 32, "Path": "effect/thunder必杀-发射", "SoundType": 2, "IsLoop": false}, {"Id": 33, "Path": "effect/下蛋飞蛋飞行", "SoundType": 2, "IsLoop": false}, {"Id": 34, "Path": "effect/人用道具的声音", "SoundType": 2, "IsLoop": false}, {"Id": 35, "Path": "effect/关闭人物窗口", "SoundType": 2, "IsLoop": false}, {"Id": 36, "Path": "effect/关闭按钮", "SoundType": 2, "IsLoop": false}, {"Id": 37, "Path": "effect/冰弹", "SoundType": 2, "IsLoop": false}, {"Id": 38, "Path": "effect/准备发射_1", "SoundType": 2, "IsLoop": false}, {"Id": 39, "Path": "effect/准备发射", "SoundType": 2, "IsLoop": false}, {"Id": 40, "Path": "effect/出经验结算条的音效", "SoundType": 2, "IsLoop": false}, {"Id": 41, "Path": "effect/切换人物攻击的时候", "SoundType": 2, "IsLoop": false}, {"Id": 42, "Path": "effect/力度", "SoundType": 2, "IsLoop": false}, {"Id": 43, "Path": "effect/单大炮爆炸", "SoundType": 2, "IsLoop": false}, {"Id": 44, "Path": "effect/单弓爆炸", "SoundType": 2, "IsLoop": false}, {"Id": 45, "Path": "effect/单电击棒爆炸", "SoundType": 2, "IsLoop": false}, {"Id": 46, "Path": "effect/单石斧爆炸", "SoundType": 2, "IsLoop": false}, {"Id": 47, "Path": "effect/单飞镖爆炸", "SoundType": 2, "IsLoop": false}, {"Id": 48, "Path": "effect/发射剪刀", "SoundType": 2, "IsLoop": false}, {"Id": 49, "Path": "effect/发射大便", "SoundType": 2, "IsLoop": false}, {"Id": 50, "Path": "effect/发射手术刀", "SoundType": 2, "IsLoop": false}, {"Id": 51, "Path": "effect/发射水果篮", "SoundType": 2, "IsLoop": false}, {"Id": 52, "Path": "effect/发射水缸", "SoundType": 2, "IsLoop": false}, {"Id": 53, "Path": "effect/发射电冰箱", "SoundType": 2, "IsLoop": false}, {"Id": 54, "Path": "effect/发射电视机", "SoundType": 2, "IsLoop": false}, {"Id": 55, "Path": "effect/发射砖板", "SoundType": 2, "IsLoop": false}, {"Id": 56, "Path": "effect/发射通马桶棍", "SoundType": 2, "IsLoop": false}, {"Id": 57, "Path": "effect/吹号", "SoundType": 2, "IsLoop": false}, {"Id": 58, "Path": "effect/嘘声", "SoundType": 2, "IsLoop": false}, {"Id": 59, "Path": "effect/场景里输入后按回车声音", "SoundType": 2, "IsLoop": false}, {"Id": 60, "Path": "effect/大闪烁", "SoundType": 2, "IsLoop": false}, {"Id": 61, "Path": "effect/好友确认添加成功", "SoundType": 2, "IsLoop": false}, {"Id": 62, "Path": "effect/完全碎裂", "SoundType": 2, "IsLoop": false}, {"Id": 63, "Path": "effect/小史莱姆移动", "SoundType": 2, "IsLoop": false}, {"Id": 64, "Path": "effect/小游戏-刚开始吸等待他人", "SoundType": 2, "IsLoop": false}, {"Id": 65, "Path": "effect/小游戏-压水", "SoundType": 2, "IsLoop": false}, {"Id": 66, "Path": "effect/小游戏-吸取部件飞出来", "SoundType": 2, "IsLoop": false}, {"Id": 67, "Path": "effect/小游戏-跳分数", "SoundType": 2, "IsLoop": false}, {"Id": 68, "Path": "effect/小游戏_吸取波谷-放弃", "SoundType": 2, "IsLoop": false}, {"Id": 69, "Path": "effect/小游戏最后额外加分", "SoundType": 2, "IsLoop": false}, {"Id": 70, "Path": "effect/小鸡爆炸", "SoundType": 2, "IsLoop": false}, {"Id": 71, "Path": "effect/建筑开启音效", "SoundType": 2, "IsLoop": false}, {"Id": 72, "Path": "effect/开功勋红包", "SoundType": 2, "IsLoop": false}, {"Id": 73, "Path": "effect/开罐子", "SoundType": 2, "IsLoop": false}, {"Id": 74, "Path": "effect/开高级罐子", "SoundType": 2, "IsLoop": false}, {"Id": 75, "Path": "effect/必杀炮声音", "SoundType": 2, "IsLoop": false}, {"Id": 76, "Path": "effect/必电击棒爆炸", "SoundType": 2, "IsLoop": false}, {"Id": 77, "Path": "effect/必飞镖爆炸", "SoundType": 2, "IsLoop": false}, {"Id": 78, "Path": "effect/总经验和总功勋音效", "SoundType": 2, "IsLoop": false}, {"Id": 79, "Path": "effect/总经验的字", "SoundType": 2, "IsLoop": false}, {"Id": 80, "Path": "effect/成功音效", "SoundType": 2, "IsLoop": false}, {"Id": 81, "Path": "effect/房间有玩家进入", "SoundType": 2, "IsLoop": false}, {"Id": 82, "Path": "effect/房间等待时吹你准备", "SoundType": 2, "IsLoop": false}, {"Id": 83, "Path": "effect/打开按钮", "SoundType": 2, "IsLoop": false}, {"Id": 84, "Path": "effect/换队伍", "SoundType": 2, "IsLoop": false}, {"Id": 85, "Path": "effect/文字输入", "SoundType": 2, "IsLoop": false}, {"Id": 86, "Path": "effect/时钟", "SoundType": 2, "IsLoop": false}, {"Id": 87, "Path": "effect/欢呼", "SoundType": 2, "IsLoop": false}, {"Id": 88, "Path": "effect/死了之后点击箱子的声音", "SoundType": 2, "IsLoop": false}, {"Id": 89, "Path": "effect/死亡的声音", "SoundType": 2, "IsLoop": false}, {"Id": 90, "Path": "effect/游戏开始前", "SoundType": 2, "IsLoop": false}, {"Id": 91, "Path": "effect/游戏结束选择牌", "SoundType": 2, "IsLoop": false}, {"Id": 92, "Path": "effect/炮弹击中空中出现物品时发出的声音", "SoundType": 2, "IsLoop": false}, {"Id": 93, "Path": "effect/炮弹击中空中物品时发出的声音", "SoundType": 2, "IsLoop": false}, {"Id": 94, "Path": "effect/点亮飘经验的音效_c", "SoundType": 2, "IsLoop": false}, {"Id": 95, "Path": "effect/烟花爆炸", "SoundType": 2, "IsLoop": false}, {"Id": 96, "Path": "effect/煎蛋与鸡蛋爆炸", "SoundType": 2, "IsLoop": false}, {"Id": 97, "Path": "effect/煎蛋发射", "SoundType": 2, "IsLoop": false}, {"Id": 98, "Path": "effect/爆炸大便", "SoundType": 2, "IsLoop": false}, {"Id": 99, "Path": "effect/爆炸手术刀", "SoundType": 2, "IsLoop": false}, {"Id": 100, "Path": "effect/爆炸手术剪", "SoundType": 2, "IsLoop": false}, {"Id": 101, "Path": "effect/爆炸柿子", "SoundType": 2, "IsLoop": false}, {"Id": 102, "Path": "effect/爆炸榴莲", "SoundType": 2, "IsLoop": false}, {"Id": 103, "Path": "effect/爆炸水缸", "SoundType": 2, "IsLoop": false}, {"Id": 104, "Path": "effect/爆炸电冰箱", "SoundType": 2, "IsLoop": false}, {"Id": 105, "Path": "effect/爆炸电视", "SoundType": 2, "IsLoop": false}, {"Id": 106, "Path": "effect/爆炸砖头", "SoundType": 2, "IsLoop": false}, {"Id": 107, "Path": "effect/爆炸通马桶棍", "SoundType": 2, "IsLoop": false}, {"Id": 108, "Path": "effect/物品回收", "SoundType": 2, "IsLoop": false}, {"Id": 109, "Path": "effect/礼炮音效", "SoundType": 2, "IsLoop": false}, {"Id": 110, "Path": "effect/系统消息_1", "SoundType": 2, "IsLoop": false}, {"Id": 111, "Path": "effect/系统消息", "SoundType": 2, "IsLoop": false}, {"Id": 112, "Path": "effect/胜利后", "SoundType": 2, "IsLoop": false}, {"Id": 113, "Path": "effect/胜利的勋章音效", "SoundType": 2, "IsLoop": false}, {"Id": 114, "Path": "effect/船长压地（大音量）", "SoundType": 2, "IsLoop": false}, {"Id": 115, "Path": "effect/获得道具展开", "SoundType": 2, "IsLoop": false}, {"Id": 116, "Path": "effect/蛋壳爆炸", "SoundType": 2, "IsLoop": false}, {"Id": 117, "Path": "effect/行走声音", "SoundType": 2, "IsLoop": false}, {"Id": 118, "Path": "effect/调开火角度", "SoundType": 2, "IsLoop": false}, {"Id": 119, "Path": "effect/跑数字单音", "SoundType": 2, "IsLoop": false}, {"Id": 120, "Path": "effect/轮到自己攻击", "SoundType": 2, "IsLoop": false}, {"Id": 121, "Path": "effect/返回", "SoundType": 2, "IsLoop": false}, {"Id": 122, "Path": "effect/选择场景按钮", "SoundType": 2, "IsLoop": false}, {"Id": 123, "Path": "effect/选择衣服类", "SoundType": 2, "IsLoop": false}, {"Id": 124, "Path": "effect/道具获得音效", "SoundType": 2, "IsLoop": false}, {"Id": 125, "Path": "effect/钟声改", "SoundType": 2, "IsLoop": false}, {"Id": 126, "Path": "effect/验证码回答正确", "SoundType": 2, "IsLoop": false}, {"Id": 127, "Path": "effect/验证码回答错误", "SoundType": 2, "IsLoop": false}, {"Id": 128, "Path": "effect/鬼魂移动", "SoundType": 2, "IsLoop": false}, {"Id": 129, "Path": "effect/鸡蛋发射1", "SoundType": 2, "IsLoop": false}, {"Id": 130, "Path": "effect/鸡蛋发射2", "SoundType": 2, "IsLoop": false}, {"Id": 131, "Path": "bg/062", "SoundType": 1, "IsLoop": true}, {"Id": 132, "Path": "bg/063", "SoundType": 1, "IsLoop": true}, {"Id": 133, "Path": "bg/064", "SoundType": 1, "IsLoop": true}, {"Id": 134, "Path": "bg/065", "SoundType": 1, "IsLoop": true}, {"Id": 135, "Path": "bg/1001", "SoundType": 1, "IsLoop": true}, {"Id": 136, "Path": "bg/1002", "SoundType": 1, "IsLoop": true}, {"Id": 137, "Path": "bg/1003", "SoundType": 1, "IsLoop": true}, {"Id": 138, "Path": "bg/1004", "SoundType": 1, "IsLoop": true}, {"Id": 139, "Path": "bg/1005", "SoundType": 1, "IsLoop": true}, {"Id": 140, "Path": "bg/1006", "SoundType": 1, "IsLoop": true}, {"Id": 141, "Path": "bg/1007", "SoundType": 1, "IsLoop": true}, {"Id": 142, "Path": "bg/1008", "SoundType": 1, "IsLoop": true}, {"Id": 143, "Path": "bg/1009", "SoundType": 1, "IsLoop": true}, {"Id": 144, "Path": "bg/1010", "SoundType": 1, "IsLoop": true}, {"Id": 145, "Path": "bg/1011", "SoundType": 1, "IsLoop": true}, {"Id": 146, "Path": "bg/1012", "SoundType": 1, "IsLoop": true}, {"Id": 147, "Path": "bg/1013", "SoundType": 1, "IsLoop": true}, {"Id": 148, "Path": "bg/1014", "SoundType": 1, "IsLoop": true}, {"Id": 149, "Path": "bg/1023", "SoundType": 1, "IsLoop": true}, {"Id": 150, "Path": "bg/1024", "SoundType": 1, "IsLoop": true}, {"Id": 151, "Path": "bg/1025", "SoundType": 1, "IsLoop": true}, {"Id": 152, "Path": "bg/1026", "SoundType": 1, "IsLoop": true}, {"Id": 153, "Path": "bg/1027", "SoundType": 1, "IsLoop": true}, {"Id": 154, "Path": "bg/1028", "SoundType": 1, "IsLoop": true}, {"Id": 155, "Path": "bg/1029", "SoundType": 1, "IsLoop": true}, {"Id": 156, "Path": "bg/1030", "SoundType": 1, "IsLoop": true}, {"Id": 157, "Path": "bg/1031", "SoundType": 1, "IsLoop": true}, {"Id": 158, "Path": "bg/1032", "SoundType": 1, "IsLoop": true}, {"Id": 159, "Path": "bg/1033", "SoundType": 1, "IsLoop": true}, {"Id": 160, "Path": "bg/1034", "SoundType": 1, "IsLoop": true}, {"Id": 161, "Path": "bg/1035", "SoundType": 1, "IsLoop": true}, {"Id": 162, "Path": "bg/1036", "SoundType": 1, "IsLoop": true}, {"Id": 163, "Path": "bg/1037", "SoundType": 1, "IsLoop": true}, {"Id": 164, "Path": "bg/1038", "SoundType": 1, "IsLoop": true}, {"Id": 165, "Path": "bg/1039", "SoundType": 1, "IsLoop": true}, {"Id": 166, "Path": "bg/1040", "SoundType": 1, "IsLoop": true}, {"Id": 167, "Path": "bg/1041", "SoundType": 1, "IsLoop": true}, {"Id": 168, "Path": "bg/1059", "SoundType": 1, "IsLoop": true}, {"Id": 169, "Path": "bg/1060", "SoundType": 1, "IsLoop": true}, {"Id": 170, "Path": "bg/1061", "SoundType": 1, "IsLoop": true}, {"Id": 171, "Path": "bg/1062", "SoundType": 1, "IsLoop": true}, {"Id": 172, "Path": "bg/1063", "SoundType": 1, "IsLoop": true}, {"Id": 173, "Path": "bg/1065", "SoundType": 1, "IsLoop": true}, {"Id": 174, "Path": "bg/1066", "SoundType": 1, "IsLoop": true}, {"Id": 175, "Path": "bg/1067", "SoundType": 1, "IsLoop": true}, {"Id": 176, "Path": "bg/1068", "SoundType": 1, "IsLoop": true}, {"Id": 177, "Path": "bg/1069", "SoundType": 1, "IsLoop": true}, {"Id": 178, "Path": "bg/1071", "SoundType": 1, "IsLoop": true}, {"Id": 179, "Path": "bg/1072", "SoundType": 1, "IsLoop": true}, {"Id": 180, "Path": "bg/1073", "SoundType": 1, "IsLoop": true}, {"Id": 181, "Path": "bg/1074", "SoundType": 1, "IsLoop": true}, {"Id": 182, "Path": "bg/1075", "SoundType": 1, "IsLoop": true}, {"Id": 183, "Path": "bg/1076", "SoundType": 1, "IsLoop": true}, {"Id": 184, "Path": "bg/1077", "SoundType": 1, "IsLoop": true}, {"Id": 185, "Path": "bg/1078", "SoundType": 1, "IsLoop": true}, {"Id": 186, "Path": "bg/1079", "SoundType": 1, "IsLoop": true}, {"Id": 187, "Path": "bg/1084", "SoundType": 1, "IsLoop": true}, {"Id": 188, "Path": "bg/11006", "SoundType": 1, "IsLoop": true}, {"Id": 189, "Path": "bg/11007", "SoundType": 1, "IsLoop": true}, {"Id": 190, "Path": "bg/11008", "SoundType": 1, "IsLoop": true}, {"Id": 191, "Path": "bg/11009", "SoundType": 1, "IsLoop": true}, {"Id": 192, "Path": "bg/11010", "SoundType": 1, "IsLoop": true}, {"Id": 193, "Path": "bg/11011", "SoundType": 1, "IsLoop": true}, {"Id": 194, "Path": "bg/11012", "SoundType": 1, "IsLoop": true}, {"Id": 195, "Path": "bg/11013", "SoundType": 1, "IsLoop": true}, {"Id": 196, "Path": "bg/11014", "SoundType": 1, "IsLoop": true}, {"Id": 197, "Path": "bg/11015", "SoundType": 1, "IsLoop": true}, {"Id": 198, "Path": "bg/11016", "SoundType": 1, "IsLoop": true}, {"Id": 199, "Path": "bg/11017", "SoundType": 1, "IsLoop": true}, {"Id": 200, "Path": "bg/11018", "SoundType": 1, "IsLoop": true}, {"Id": 201, "Path": "bg/11019", "SoundType": 1, "IsLoop": true}, {"Id": 202, "Path": "bg/11020", "SoundType": 1, "IsLoop": true}, {"Id": 203, "Path": "bg/11021", "SoundType": 1, "IsLoop": true}, {"Id": 204, "Path": "bg/11022", "SoundType": 1, "IsLoop": true}, {"Id": 205, "Path": "bg/11023", "SoundType": 1, "IsLoop": true}, {"Id": 206, "Path": "bg/11024", "SoundType": 1, "IsLoop": true}, {"Id": 207, "Path": "bg/11025", "SoundType": 1, "IsLoop": true}, {"Id": 208, "Path": "bg/11029", "SoundType": 1, "IsLoop": true}, {"Id": 209, "Path": "bg/11030", "SoundType": 1, "IsLoop": true}, {"Id": 210, "Path": "bg/11031", "SoundType": 1, "IsLoop": true}, {"Id": 211, "Path": "bg/11032", "SoundType": 1, "IsLoop": true}, {"Id": 212, "Path": "bg/11033", "SoundType": 1, "IsLoop": true}, {"Id": 213, "Path": "bg/11034", "SoundType": 1, "IsLoop": true}, {"Id": 214, "Path": "bg/11035", "SoundType": 1, "IsLoop": true}, {"Id": 215, "Path": "bg/11036", "SoundType": 1, "IsLoop": true}, {"Id": 216, "Path": "bg/11037", "SoundType": 1, "IsLoop": true}, {"Id": 217, "Path": "bg/11038", "SoundType": 1, "IsLoop": true}, {"Id": 218, "Path": "bg/11039", "SoundType": 1, "IsLoop": true}, {"Id": 219, "Path": "bg/11040", "SoundType": 1, "IsLoop": true}, {"Id": 220, "Path": "bg/11041", "SoundType": 1, "IsLoop": true}, {"Id": 221, "Path": "bg/11042", "SoundType": 1, "IsLoop": true}, {"Id": 222, "Path": "bg/11043", "SoundType": 1, "IsLoop": true}, {"Id": 223, "Path": "bg/11044", "SoundType": 1, "IsLoop": true}, {"Id": 224, "Path": "bg/11045", "SoundType": 1, "IsLoop": true}, {"Id": 225, "Path": "bg/11046", "SoundType": 1, "IsLoop": true}, {"Id": 226, "Path": "bg/11047", "SoundType": 1, "IsLoop": true}, {"Id": 227, "Path": "bg/11048", "SoundType": 1, "IsLoop": true}, {"Id": 228, "Path": "bg/1119", "SoundType": 1, "IsLoop": true}, {"Id": 229, "Path": "bg/1120", "SoundType": 1, "IsLoop": true}, {"Id": 230, "Path": "bg/1121", "SoundType": 1, "IsLoop": true}, {"Id": 231, "Path": "bg/1122", "SoundType": 1, "IsLoop": true}, {"Id": 232, "Path": "bg/1123", "SoundType": 1, "IsLoop": true}, {"Id": 233, "Path": "bg/1124", "SoundType": 1, "IsLoop": true}, {"Id": 234, "Path": "bg/1125", "SoundType": 1, "IsLoop": true}, {"Id": 235, "Path": "bg/1126", "SoundType": 1, "IsLoop": true}, {"Id": 236, "Path": "bg/1127", "SoundType": 1, "IsLoop": true}, {"Id": 237, "Path": "bg/1128", "SoundType": 1, "IsLoop": true}, {"Id": 238, "Path": "bg/1142", "SoundType": 1, "IsLoop": true}, {"Id": 239, "Path": "bg/1143", "SoundType": 1, "IsLoop": true}, {"Id": 240, "Path": "bg/1144", "SoundType": 1, "IsLoop": true}, {"Id": 241, "Path": "bg/1145", "SoundType": 1, "IsLoop": true}, {"Id": 242, "Path": "bg/1146", "SoundType": 1, "IsLoop": true}, {"Id": 243, "Path": "bg/1147", "SoundType": 1, "IsLoop": true}, {"Id": 244, "Path": "bg/1148", "SoundType": 1, "IsLoop": true}, {"Id": 245, "Path": "bg/1149", "SoundType": 1, "IsLoop": true}, {"Id": 246, "Path": "bg/1151", "SoundType": 1, "IsLoop": true}, {"Id": 247, "Path": "bg/1152", "SoundType": 1, "IsLoop": true}, {"Id": 248, "Path": "bg/1153", "SoundType": 1, "IsLoop": true}, {"Id": 249, "Path": "bg/1154", "SoundType": 1, "IsLoop": true}, {"Id": 250, "Path": "bg/1156", "SoundType": 1, "IsLoop": true}, {"Id": 251, "Path": "bg/1158", "SoundType": 1, "IsLoop": true}, {"Id": 252, "Path": "bg/1159", "SoundType": 1, "IsLoop": true}, {"Id": 253, "Path": "bg/1160", "SoundType": 1, "IsLoop": true}, {"Id": 254, "Path": "bg/1161", "SoundType": 1, "IsLoop": true}, {"Id": 255, "Path": "bg/1162", "SoundType": 1, "IsLoop": true}, {"Id": 256, "Path": "bg/1163", "SoundType": 1, "IsLoop": true}, {"Id": 257, "Path": "bg/1164", "SoundType": 1, "IsLoop": true}, {"Id": 258, "Path": "bg/1165", "SoundType": 1, "IsLoop": true}, {"Id": 259, "Path": "bg/1166", "SoundType": 1, "IsLoop": true}, {"Id": 260, "Path": "bg/1167", "SoundType": 1, "IsLoop": true}, {"Id": 261, "Path": "bg/1168", "SoundType": 1, "IsLoop": true}, {"Id": 262, "Path": "bg/1180", "SoundType": 1, "IsLoop": true}, {"Id": 263, "Path": "bg/1184", "SoundType": 1, "IsLoop": true}, {"Id": 264, "Path": "bg/1185", "SoundType": 1, "IsLoop": true}, {"Id": 265, "Path": "bg/1186", "SoundType": 1, "IsLoop": true}, {"Id": 266, "Path": "bg/1187", "SoundType": 1, "IsLoop": true}, {"Id": 267, "Path": "bg/1188", "SoundType": 1, "IsLoop": true}, {"Id": 268, "Path": "bg/1189", "SoundType": 1, "IsLoop": true}, {"Id": 269, "Path": "bg/1190", "SoundType": 1, "IsLoop": true}, {"Id": 270, "Path": "bg/1191", "SoundType": 1, "IsLoop": true}, {"Id": 271, "Path": "bg/1192", "SoundType": 1, "IsLoop": true}, {"Id": 272, "Path": "bg/1193", "SoundType": 1, "IsLoop": true}, {"Id": 273, "Path": "bg/1194", "SoundType": 1, "IsLoop": true}, {"Id": 274, "Path": "bg/1195", "SoundType": 1, "IsLoop": true}, {"Id": 275, "Path": "bg/1196", "SoundType": 1, "IsLoop": true}, {"Id": 276, "Path": "bg/1197", "SoundType": 1, "IsLoop": true}, {"Id": 277, "Path": "bg/1198", "SoundType": 1, "IsLoop": true}, {"Id": 278, "Path": "bg/12001", "SoundType": 1, "IsLoop": true}, {"Id": 279, "Path": "bg/12002", "SoundType": 1, "IsLoop": true}, {"Id": 280, "Path": "bg/12003", "SoundType": 1, "IsLoop": true}, {"Id": 281, "Path": "bg/12004", "SoundType": 1, "IsLoop": true}, {"Id": 282, "Path": "bg/12005", "SoundType": 1, "IsLoop": true}, {"Id": 283, "Path": "bg/12006", "SoundType": 1, "IsLoop": true}, {"Id": 284, "Path": "bg/12007", "SoundType": 1, "IsLoop": true}, {"Id": 285, "Path": "bg/12008", "SoundType": 1, "IsLoop": true}, {"Id": 286, "Path": "bg/12009", "SoundType": 1, "IsLoop": true}, {"Id": 287, "Path": "bg/1201", "SoundType": 1, "IsLoop": true}, {"Id": 288, "Path": "bg/12010", "SoundType": 1, "IsLoop": true}, {"Id": 289, "Path": "bg/12011", "SoundType": 1, "IsLoop": true}, {"Id": 290, "Path": "bg/12012", "SoundType": 1, "IsLoop": true}, {"Id": 291, "Path": "bg/12013", "SoundType": 1, "IsLoop": true}, {"Id": 292, "Path": "bg/12014", "SoundType": 1, "IsLoop": true}, {"Id": 293, "Path": "bg/12015", "SoundType": 1, "IsLoop": true}, {"Id": 294, "Path": "bg/12016", "SoundType": 1, "IsLoop": true}, {"Id": 295, "Path": "bg/12017", "SoundType": 1, "IsLoop": true}, {"Id": 296, "Path": "bg/12018", "SoundType": 1, "IsLoop": true}, {"Id": 297, "Path": "bg/12019", "SoundType": 1, "IsLoop": true}, {"Id": 298, "Path": "bg/12020", "SoundType": 1, "IsLoop": true}, {"Id": 299, "Path": "bg/12025", "SoundType": 1, "IsLoop": true}, {"Id": 300, "Path": "bg/12026", "SoundType": 1, "IsLoop": true}, {"Id": 301, "Path": "bg/12027", "SoundType": 1, "IsLoop": true}, {"Id": 302, "Path": "bg/12028", "SoundType": 1, "IsLoop": true}, {"Id": 303, "Path": "bg/12029", "SoundType": 1, "IsLoop": true}, {"Id": 304, "Path": "bg/12030", "SoundType": 1, "IsLoop": true}, {"Id": 305, "Path": "bg/1204", "SoundType": 1, "IsLoop": true}, {"Id": 306, "Path": "bg/1205", "SoundType": 1, "IsLoop": true}, {"Id": 307, "Path": "bg/1207", "SoundType": 1, "IsLoop": true}, {"Id": 308, "Path": "bg/1208", "SoundType": 1, "IsLoop": true}, {"Id": 309, "Path": "bg/1209", "SoundType": 1, "IsLoop": true}, {"Id": 310, "Path": "bg/1210", "SoundType": 1, "IsLoop": true}, {"Id": 311, "Path": "bg/1211", "SoundType": 1, "IsLoop": true}, {"Id": 312, "Path": "bg/1214", "SoundType": 1, "IsLoop": true}, {"Id": 313, "Path": "bg/1215", "SoundType": 1, "IsLoop": true}, {"Id": 314, "Path": "bg/1216", "SoundType": 1, "IsLoop": true}, {"Id": 315, "Path": "bg/1217", "SoundType": 1, "IsLoop": true}, {"Id": 316, "Path": "bg/1248", "SoundType": 1, "IsLoop": true}, {"Id": 317, "Path": "bg/1328", "SoundType": 1, "IsLoop": true}, {"Id": 318, "Path": "bg/1342", "SoundType": 1, "IsLoop": true}, {"Id": 319, "Path": "bg/1343", "SoundType": 1, "IsLoop": true}, {"Id": 320, "Path": "bg/1344", "SoundType": 1, "IsLoop": true}, {"Id": 321, "Path": "bg/1345", "SoundType": 1, "IsLoop": true}, {"Id": 322, "Path": "bg/140", "SoundType": 1, "IsLoop": true}, {"Id": 323, "Path": "bg/1400", "SoundType": 1, "IsLoop": true}, {"Id": 324, "Path": "bg/1401", "SoundType": 1, "IsLoop": true}, {"Id": 325, "Path": "bg/2001", "SoundType": 1, "IsLoop": true}, {"Id": 326, "Path": "bg/3000", "SoundType": 1, "IsLoop": true}, {"Id": 327, "Path": "bg/3001", "SoundType": 1, "IsLoop": true}, {"Id": 328, "Path": "bg/3002", "SoundType": 1, "IsLoop": true}, {"Id": 329, "Path": "bg/3003", "SoundType": 1, "IsLoop": true}, {"Id": 330, "Path": "bg/3004", "SoundType": 1, "IsLoop": true}, {"Id": 331, "Path": "bg/fin", "SoundType": 1, "IsLoop": true}, {"Id": 332, "Path": "bg/qx01", "SoundType": 1, "IsLoop": true}, {"Id": 333, "Path": "bg/wenquan_1", "SoundType": 1, "IsLoop": true}, {"Id": 334, "Path": "bg/worldbossfight-1", "SoundType": 1, "IsLoop": true}, {"Id": 335, "Path": "bg/worldbossfight-2", "SoundType": 1, "IsLoop": true}, {"Id": 336, "Path": "bg/worldbossfight-4", "SoundType": 1, "IsLoop": true}, {"Id": 337, "Path": "bg/worldbossfight", "SoundType": 1, "IsLoop": true}, {"Id": 338, "Path": "bg/worldbossroom-1", "SoundType": 1, "IsLoop": true}, {"Id": 339, "Path": "bg/worldbossroom-2", "SoundType": 1, "IsLoop": true}, {"Id": 340, "Path": "bg/worldbossroom-4", "SoundType": 1, "IsLoop": true}, {"Id": 341, "Path": "bg/worldbossroom", "SoundType": 1, "IsLoop": true}, {"Id": 342, "Path": "bg/xxgbj1", "SoundType": 1, "IsLoop": true}, {"Id": 343, "Path": "bg/xxgbj2", "SoundType": 1, "IsLoop": true}]}