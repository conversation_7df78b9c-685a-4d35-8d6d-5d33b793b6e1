{"version": 3, "sources": ["file:///C:/DDT_Classic_Cocos/trunk/client/project/assets/script/module/room_comm/model/RoomModel.ts"], "names": ["Tween", "tween", "cfg", "L<PERSON>ey", "GBaseModel", "LangMgr", "ModelDecorator", "TipDirection", "TipInfo", "UITipsManager", "CommonTextTip", "RoomModel", "getPropBean", "id", "prop<PERSON>ean", "tables", "Shop_GoodsTable", "get", "getPveRoomInfo", "bean", "Pve_InfoTable", "getPveLvLimitArray", "field", "undefined", "split", "getPveLvLimitNumberArray", "array", "length", "map", "section", "leftPart", "Number", "wrapText", "input", "maxPerLine", "result", "count", "c", "push", "join", "replace", "getSkillId", "index", "DOUBLEEXPCARD", "DOUBLEMERITCARD", "PREVENTKICKCARD", "resetNumbers", "indexNums", "i", "j", "Math", "floor", "random", "currentIndex", "getNextRandomNumber", "num", "getFriendshipTips", "MovingNotificationTable", "inst", "getText", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "friendshipTipsTween", "content", "height", "tag", "setPosition", "x", "text", "t1", "to", "y", "easing", "delay", "t2", "call", "sequence", "repeatF<PERSON><PERSON>", "start", "stopTween", "stopAllByTag", "showTextTip", "btn", "tipDir", "TOP_CENTER", "tipBase", "visibility", "tipInfo", "uiName", "target", "tipsDirections", "params", "showTip", "then", "uiTips", "HideTextTip", "getTextFormat", "name", "des", "power", "temp", "C_ddt_pets_skillTipLost", "C_energy", "C_ddt_pets_skillTipDesc", "onRegister", "onRemove", "Entrance", "RoomType"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACSA,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;;AACPC,MAAAA,G,SAAAA,G;AAAKC,MAAAA,I,SAAAA,I;;AAELC,MAAAA,U,aAAAA,U;AAAYC,MAAAA,O,aAAAA,O;AAASC,MAAAA,c,aAAAA,c;AAAyBC,MAAAA,Y,aAAAA,Y;AAAcC,MAAAA,O,aAAAA,O;AAASC,MAAAA,a,aAAAA,a;;AACrEC,MAAAA,a,iBAAAA,a;;;;;;;;;2BAGIC,S,WADZ;AAAA;AAAA,6C,2BAAD,MACaA,SADb;AAAA;AAAA,oCAC0C;AAAA;AAAA;;AAAA;;AAAA,iDAGG,KAHH;;AAAA,mDAIK,KAJL;;AAAA,mDAKK,KALL;;AAAA,6CAOR,EAPQ;;AAAA,gDAQP,CARO;;AAAA,kDASG,CATH;;AAAA,uDAUC,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAVD;;AAAA,sDAWO,KAXP;;AAAA,sDAYO,KAZP;AAAA;;AAatC;AAEA;AACOC,QAAAA,WAAW,CAACC,EAAD,EAAwC;AACtD,cAAIC,QAAQ,GAAG;AAAA;AAAA,0BAAIC,MAAJ,CAAWC,eAAX,CAA2BC,GAA3B,CAA+BJ,EAA/B,CAAf;;AACA,cAAIC,QAAQ,KAAK,IAAjB,EAAuB;AACnB,mBAAOA,QAAP;AACH;;AACD,iBAAO,IAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWI,QAAAA,cAAc,CAACL,EAAD,EAAoC;AACrD;AACA,cAAIM,IAAI,GAAG;AAAA;AAAA,0BAAIJ,MAAJ,CAAWK,aAAX,CAAyBH,GAAzB,CAA6BJ,EAA7B,CAAX;;AACA,cAAIM,IAAI,KAAK,IAAb,EAAmB;AACf,mBAAOA,IAAP;AACH;;AACD,iBAAO,IAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACWE,QAAAA,kBAAkB,CAACC,KAAD,EAA0B;AAC/C,cAAIA,KAAK,IAAI,IAAT,IAAiBA,KAAK,IAAIC,SAA9B,EAAyC,OAAO,EAAP;AACzC,iBAAOD,KAAK,CAACE,KAAN,CAAY,GAAZ,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACWC,QAAAA,wBAAwB,CAACC,KAAD,EAA4B;AACvD,cAAIA,KAAK,CAACC,MAAN,IAAgB,CAApB,EAAuB,OAAO,EAAP;AAEvB,iBAAOD,KAAK,CAACE,GAAN,CAAUC,OAAO,IAAI;AACxB,gBAAMC,QAAQ,GAAGD,OAAO,CAACL,KAAR,CAAc,GAAd,EAAmB,CAAnB,CAAjB;AACA,mBAAOO,MAAM,CAACD,QAAD,CAAb;AACH,WAHM,CAAP;AAIH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACWE,QAAAA,QAAQ,CAACC,KAAD,EAAiD;AAAA,cAAjCC,UAAiC,uEAAZ,EAAY;AAC5D,cAAI,CAACD,KAAL,EAAY,OAAO,EAAP;AAEZ,cAAIE,MAAgB,GAAG,EAAvB;AACA,cAAIC,KAAK,GAAG,CAAZ;;AAEA,eAAK,IAAMC,CAAX,IAAgBJ,KAAhB,EAAuB;AACnBE,YAAAA,MAAM,CAACG,IAAP,CAAYD,CAAZ;AACAD,YAAAA,KAAK;;AACL,gBAAIA,KAAK,GAAGF,UAAR,KAAuB,CAA3B,EAA8B;AAC1BC,cAAAA,MAAM,CAACG,IAAP,CAAY,IAAZ;AACH;AACJ;;AAED,iBAAOH,MAAM,CAACI,IAAP,CAAY,EAAZ,EAAgBC,OAAhB,CAAwB,KAAxB,EAA+B,EAA/B,CAAP;AACH;;AAEMC,QAAAA,UAAU,CAACC,KAAD,EAAwB;AACrC,cAAIA,KAAK,IAAI,CAAb,EAAgB;AACZ,mBAAO,KAAKC,aAAZ;AACH,WAFD,MAEO,IAAID,KAAK,IAAI,CAAb,EAAgB;AACnB,mBAAO,KAAKE,eAAZ;AACH,WAFM,MAEA,IAAIF,KAAK,IAAI,CAAb,EAAgB;AACnB,mBAAO,KAAKG,eAAZ;AACH,WAFM,MAEA;AACH;AACA,mBAAO,CAAP;AACH;AACJ;;AAEOC,QAAAA,YAAY,GAAS;AACzB,eAAKC,SAAL,GAAiB,EAAjB;;AAEA,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI,EAArB,EAAyBA,CAAC,EAA1B,EAA8B;AAC1B,iBAAKD,SAAL,CAAeT,IAAf,CAAoBU,CAApB;AACH;;AAED,eAAK,IAAIA,EAAC,GAAG,KAAKD,SAAL,CAAepB,MAAf,GAAwB,CAArC,EAAwCqB,EAAC,GAAG,CAA5C,EAA+CA,EAAC,EAAhD,EAAoD;AAChD,gBAAMC,CAAC,GAAGC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,MAAiBJ,EAAC,GAAG,CAArB,CAAX,CAAV;AACA,aAAC,KAAKD,SAAL,CAAeC,EAAf,CAAD,EAAoB,KAAKD,SAAL,CAAeE,CAAf,CAApB,IAAyC,CAAC,KAAKF,SAAL,CAAeE,CAAf,CAAD,EAAoB,KAAKF,SAAL,CAAeC,EAAf,CAApB,CAAzC;AACH;;AAED,eAAKK,YAAL,GAAoB,CAApB;AACH;;AAEOC,QAAAA,mBAAmB,GAAW;AAClC,cAAI,KAAKD,YAAL,IAAqB,KAAKN,SAAL,CAAepB,MAAxC,EAAgD;AAC5C,iBAAKmB,YAAL;AACH;;AAED,cAAMS,GAAG,GAAG,KAAKR,SAAL,CAAe,KAAKM,YAApB,CAAZ;AACA,eAAKA,YAAL;AAEA,iBAAOE,GAAP;AACH;;AAEMC,QAAAA,iBAAiB,GAAW;AAC/B,cAAI3C,EAAE,GAAG,KAAKyC,mBAAL,EAAT;AACA,cAAInC,IAAI,GAAG;AAAA;AAAA,0BAAIJ,MAAJ,CAAW0C,uBAAX,CAAmCxC,GAAnC,CAAuCJ,EAAvC,CAAX;;AACA,cAAIM,IAAI,KAAK,IAAb,EAAmB;AACf,mBAAO;AAAA;AAAA,oCAAQuC,IAAR,GAAeC,OAAf,CAAuBxC,IAAI,CAACyC,WAA5B,CAAP;AACH;;AAED,iBAAO,EAAP;AACH;;AAEMC,QAAAA,mBAAmB,CAACC,OAAD,EAAsBC,MAAtB,EAAsCC,GAAtC,EAAyD;AAC/E,cAAIF,OAAO,IAAI,IAAX,IAAmBA,OAAO,IAAIvC,SAAlC,EAA6C;AACzC;AACH;;AACDuC,UAAAA,OAAO,CAACG,WAAR,CAAoBH,OAAO,CAACI,CAA5B,EAA+BH,MAA/B;AACAD,UAAAA,OAAO,CAACK,IAAR,GAAe,KAAKX,iBAAL,EAAf;AAEA,cAAIY,EAAE,GAAGnE,KAAK,CAAC6D,OAAD,CAAL,CACJO,EADI,CACD,GADC,EACI;AAAEC,YAAAA,CAAC,EAAER,OAAO,CAACQ,CAAR,GAAYP;AAAjB,WADJ,EAC+B;AAAEQ,YAAAA,MAAM,EAAE;AAAV,WAD/B,EAEJC,KAFI,CAEE,EAFF,CAAT;AAGA,cAAIC,EAAE,GAAGxE,KAAK,CAAC6D,OAAD,CAAL,CACJO,EADI,CACD,GADC,EACI;AAAEC,YAAAA,CAAC,EAAE,CAACP;AAAN,WADJ,EACoB;AAAEQ,YAAAA,MAAM,EAAE;AAAV,WADpB,EAEJG,IAFI,CAEC,MAAM;AACRZ,YAAAA,OAAO,CAACG,WAAR,CAAoBH,OAAO,CAACI,CAA5B,EAA+BH,MAA/B;AACAD,YAAAA,OAAO,CAACK,IAAR,GAAe,KAAKX,iBAAL,EAAf;AACH,WALI,CAAT;AAMAvD,UAAAA,KAAK,CAAC6D,OAAD,CAAL,CAAeE,GAAf,CAAmBA,GAAnB,EAAwBW,QAAxB,CAAiCP,EAAjC,EAAqCK,EAArC,EAAyCG,aAAzC,GAAyDC,KAAzD;AACH;;AAEMC,QAAAA,SAAS,CAACd,GAAD,EAAoB;AAChChE,UAAAA,KAAK,CAAC+E,YAAN,CAAmBf,GAAnB;AACH;;AAEMgB,QAAAA,WAAW,CAACC,GAAD,EAAenB,OAAf,EAAsF;AAAA,cAAtDoB,MAAsD,uEAA/B;AAAA;AAAA,4CAAaC,UAAkB;AACpG,cAAIF,GAAG,IAAI,IAAP,IAAeA,GAAG,IAAI1D,SAA1B,EAAqC;;AAErC,cAAI,KAAK6D,OAAL,IAAgB,IAApB,EAA0B;AACtB,iBAAKA,OAAL,CAAaC,UAAb,GAA0B,IAA1B;AACH;;AACD,cAAIC,OAAO,GAAG;AAAA;AAAA,mCAAd;AACAA,UAAAA,OAAO,CAACC,MAAR;AAAA;AAAA;AACAD,UAAAA,OAAO,CAACE,MAAR,GAAiBP,GAAjB;AACAK,UAAAA,OAAO,CAACG,cAAR,GAAyB,CAACP,MAAD,CAAzB;AACAI,UAAAA,OAAO,CAACI,MAAR,GAAiB5B,OAAjB;AACA;AAAA;AAAA,8CAAcJ,IAAd,GACKiC,OADL,CACaL,OADb,EAEKM,IAFL,CAEUC,MAAM,IAAI;AACZ,iBAAKT,OAAL,GAAeS,MAAf;AACH,WAJL;AAKH;;AAEMC,QAAAA,WAAW,GAAS;AACvB,cAAI,KAAKV,OAAL,IAAgB,IAApB,EAA0B;AACtB,iBAAKA,OAAL,CAAaC,UAAb,GAA0B,KAA1B;AACH;AACJ,SAtLqC,CAwLtC;;;AACOU,QAAAA,aAAa,CAACC,IAAD,EAAeC,GAAf,EAA4BC,KAA5B,EAAmD;AACnE,cAAIC,IAAI,GAAG,4BAAqBH,IAArB,oBACP,IADO,gBAED;AAAA;AAAA,kCAAQtC,IAAR,GAAeC,OAAf,CAAuB;AAAA;AAAA,4BAAKyC,uBAA5B,CAFC,uCAGWF,KAHX,gBAIP;AAAA;AAAA,kCAAQxC,IAAR,GAAeC,OAAf,CAAuB;AAAA;AAAA,4BAAK0C,QAA5B,CAJO,GAKP,IALO,gBAMD;AAAA;AAAA,kCAAQ3C,IAAR,GAAeC,OAAf,CAAuB;AAAA;AAAA,4BAAK2C,uBAA5B,CANC,cAOP,IAPO,GAQPL,GARJ;AASA,oCAAmBE,IAAnB;AACH;;AAEDI,QAAAA,UAAU,GAAS;AACf,eAAKzD,YAAL;AACH;;AAED0D,QAAAA,QAAQ,GAAS,CAAG;;AA1MkB,O,wCACJ,W;;0BA4M1BC,Q,0BAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;eAAAA,Q;;;0BAKAC,Q,0BAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;eAAAA,Q", "sourcesContent": ["import { Pve_Info, Shop_Goods } from \"auto/tableCodes/schema\";\r\nimport { Tween, tween } from \"cc\";\r\nimport { cfg, <PERSON><PERSON><PERSON> } from \"core\";\r\nimport { GButton, GTextField } from \"fairygui-cc\";\r\nimport { GBaseModel, LangMgr, ModelDecorator, TipBase, TipDirection, TipInfo, UITipsManager } from \"game-base\";\r\nimport { CommonTextTip } from \"../../comm/view/tips/CommonTextTip\";\r\n\r\n@ModelDecorator()\r\nexport class RoomModel extends GBaseModel {\r\n    public static className: string = \"RoomModel\";\r\n    private tipBase: TipBase;\r\n    private readonly DOUBLEEXPCARD: number = 11998;\r\n    private readonly DOUBLEMERITCARD: number = 11997;\r\n    private readonly PREVENTKICKCARD: number = 11996;\r\n\r\n    private indexNums: number[] = [];\r\n    private currentIndex: number = 0;\r\n    public readonly MAX_ROOM_COUNT: number = 8;\r\n    public takeAlongPropsArray: number[] = [0, 0, 0];\r\n    public readonly PVP_TIPS_TWEEN_TAG: number = 10000;\r\n    public readonly PVE_TIPS_TWEEN_TAG: number = 10001;\r\n    //public static readonly NO_SUITABLE_ROOMS: string = \"暂时没有合适的房间，请稍后再试。\";\r\n\r\n    //\r\n    public getPropBean(id: number): Shop_Goods.Shop_GoodsBean {\r\n        var propBean = cfg.tables.Shop_GoodsTable.get(id);\r\n        if (propBean !== null) {\r\n            return propBean;\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * 获取pve房间信息\r\n     * @param id 房间id\r\n     * @returns 房间信息\r\n     */\r\n    public getPveRoomInfo(id: number): Pve_Info.Pve_InfoBean {\r\n        //id与map文件夹名一致\r\n        var bean = cfg.tables.Pve_InfoTable.get(id);\r\n        if (bean !== null) {\r\n            return bean;\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * 获取pve等级限制条件\r\n     * @param field 等级限制字段\r\n     * @returns 等级限制数组\r\n     * @example 1-10|11-20|21-30 => [1-10, 11-20, 21-30]\r\n     */\r\n    public getPveLvLimitArray(field: string): string[] {\r\n        if (field == null || field == undefined) return [];\r\n        return field.split(\"|\");\r\n    }\r\n\r\n    /**\r\n     * 获取pve等级限制的左边界\r\n     * @param array 等级限制数组\r\n     * @returns 左边界数组\r\n     * @example [1-10, 11-20, 21-30] => [1, 11, 21]\r\n     */\r\n    public getPveLvLimitNumberArray(array: string[]): number[] {\r\n        if (array.length == 0) return [];\r\n\r\n        return array.map(section => {\r\n            const leftPart = section.split('-')[0];\r\n            return Number(leftPart);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 文本换行\r\n     * @param input 输入文本\r\n     * @param maxPerLine 每行最大字符数\r\n     * @returns 多少个字符换行后的文本\r\n     * @example \"1234567890\" => \"123456\\n7890\"\r\n     */\r\n    public wrapText(input: string, maxPerLine: number = 12): string {\r\n        if (!input) return \"\";\r\n\r\n        let result: string[] = [];\r\n        let count = 0;\r\n\r\n        for (const c of input) {\r\n            result.push(c);\r\n            count++;\r\n            if (count % maxPerLine === 0) {\r\n                result.push(\"\\n\");\r\n            }\r\n        }\r\n\r\n        return result.join(\"\").replace(/\\n$/, \"\");\r\n    }\r\n\r\n    public getSkillId(index: number): number {\r\n        if (index == 0) {\r\n            return this.DOUBLEEXPCARD;\r\n        } else if (index == 1) {\r\n            return this.DOUBLEMERITCARD;\r\n        } else if (index == 2) {\r\n            return this.PREVENTKICKCARD;\r\n        } else {\r\n            //配表中没找到，后续添加\r\n            return 0;\r\n        }\r\n    }\r\n\r\n    private resetNumbers(): void {\r\n        this.indexNums = [];\r\n\r\n        for (let i = 1; i <= 41; i++) {\r\n            this.indexNums.push(i);\r\n        }\r\n\r\n        for (let i = this.indexNums.length - 1; i > 0; i--) {\r\n            const j = Math.floor(Math.random() * (i + 1));\r\n            [this.indexNums[i], this.indexNums[j]] = [this.indexNums[j], this.indexNums[i]];\r\n        }\r\n\r\n        this.currentIndex = 0;\r\n    }\r\n\r\n    private getNextRandomNumber(): number {\r\n        if (this.currentIndex >= this.indexNums.length) {\r\n            this.resetNumbers();\r\n        }\r\n\r\n        const num = this.indexNums[this.currentIndex];\r\n        this.currentIndex++;\r\n\r\n        return num;\r\n    }\r\n\r\n    public getFriendshipTips(): string {\r\n        let id = this.getNextRandomNumber();\r\n        let bean = cfg.tables.MovingNotificationTable.get(id);\r\n        if (bean !== null) {\r\n            return LangMgr.inst().getText(bean.TipsContent);\r\n        }\r\n\r\n        return \"\";\r\n    }\r\n\r\n    public friendshipTipsTween(content: GTextField, height: number, tag: number): void {\r\n        if (content == null || content == undefined) {\r\n            return;\r\n        }\r\n        content.setPosition(content.x, height);\r\n        content.text = this.getFriendshipTips();\r\n\r\n        let t1 = tween(content)\r\n            .to(1.0, { y: content.y - height }, { easing: \"linear\" })\r\n            .delay(10);\r\n        let t2 = tween(content)\r\n            .to(1.0, { y: -height }, { easing: \"linear\" })\r\n            .call(() => {\r\n                content.setPosition(content.x, height);\r\n                content.text = this.getFriendshipTips();\r\n            });\r\n        tween(content).tag(tag).sequence(t1, t2).repeatForever().start();\r\n    }\r\n\r\n    public stopTween(tag: number): void {\r\n        Tween.stopAllByTag(tag);\r\n    }\r\n\r\n    public showTextTip(btn: GButton, content: string, tipDir: TipDirection = TipDirection.TOP_CENTER): void {\r\n        if (btn == null || btn == undefined) return;\r\n\r\n        if (this.tipBase != null) {\r\n            this.tipBase.visibility = true;\r\n        }\r\n        var tipInfo = new TipInfo();\r\n        tipInfo.uiName = CommonTextTip;\r\n        tipInfo.target = btn;\r\n        tipInfo.tipsDirections = [tipDir];\r\n        tipInfo.params = content;\r\n        UITipsManager.inst()\r\n            .showTip(tipInfo)\r\n            .then(uiTips => {\r\n                this.tipBase = uiTips;\r\n            });\r\n    }\r\n\r\n    public HideTextTip(): void {\r\n        if (this.tipBase != null) {\r\n            this.tipBase.visibility = false;\r\n        }\r\n    }\r\n\r\n    //C_tank_view_common_RoomIIPropTip_Energy\r\n    public getTextFormat(name: string, des: string, power: number): string {\r\n        var temp = `[b][color=#FFFF99]${name}[/b][/color]` +\r\n            \"\\n\" +\r\n            `[b]${LangMgr.inst().getText(LKey.C_ddt_pets_skillTipLost)}[/b]: ` +\r\n            `[color=#F2C834]${power}[/color]` +\r\n            LangMgr.inst().getText(LKey.C_energy) +\r\n            \"\\n\" +\r\n            `[b]${LangMgr.inst().getText(LKey.C_ddt_pets_skillTipDesc)}[/b]: ` +\r\n            \"\\n\" +\r\n            des;\r\n        return `[size=15]${temp}[/size]`;\r\n    }\r\n\r\n    onRegister(): void {\r\n        this.resetNumbers();\r\n    }\r\n\r\n    onRemove(): void { }\r\n}\r\n\r\nexport enum Entrance {\r\n    ENTER,\r\n    FIND,\r\n}\r\n\r\nexport enum RoomType {\r\n    PVP,\r\n    PVE,\r\n    //挑战赛\r\n    CHALLENGE,\r\n    //精英赛\r\n    ELITE,\r\n}\r\n"]}