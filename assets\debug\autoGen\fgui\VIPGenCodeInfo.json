[{"type": "GComponent", "name": "VipView", "res": "ui://plchh27qdt0p1", "children": [{"type": "GComponent", "name": "Bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GComponent", "name": "HeadShow", "res": "ui://dyypinptxpql1zv", "resName": "HeadShow", "package": "Comm_Comp"}, {"type": "GComponent", "name": "VipProgress", "res": "ui://plchh27qdt0p18", "resName": "VipProgress", "package": "VIP"}, {"type": "GButton", "name": "BtnVip"}, {"type": "GList", "name": "TabList"}, {"type": "GComponent", "name": "InputNmae", "res": "ui://plchh27qdt0p1a", "resName": "VipInput", "package": "VIP"}, {"type": "GComponent", "name": "InputAgain", "res": "ui://plchh27qdt0p1a", "resName": "VipInput", "package": "VIP"}, {"type": "GButton", "name": "n30"}, {"type": "GList", "name": "n20"}, {"type": "GButton", "name": "BtnSure"}, {"type": "GButton", "name": "BtnGift"}]}, {"type": "GComponent", "name": "VipDetailView", "res": "ui://plchh27qdt0p1e", "children": [{"type": "GComponent", "name": "Bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GButton", "name": "BtnSure"}, {"type": "GComponent", "name": "Content", "res": "ui://plchh27qdt0p1f", "resName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "package": "VIP", "children": [{"type": "GList", "name": "n14"}]}]}, {"type": "GComponent", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "res": "ui://plchh27qdt0p1f", "children": [{"type": "GList", "name": "n14"}]}, {"type": "GComponent", "name": "VipLevelRewardView", "res": "ui://plchh27qdt0p1v", "children": [{"type": "GComponent", "name": "Bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GButton", "name": "BtnReward"}, {"type": "GList", "name": "RewardList"}]}, {"type": "GComponent", "name": "VipRewardView", "res": "ui://plchh27qdt0p1y", "children": [{"type": "GComponent", "name": "Bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GButton", "name": "BtnReward"}, {"type": "GList", "name": "RewardList"}]}]