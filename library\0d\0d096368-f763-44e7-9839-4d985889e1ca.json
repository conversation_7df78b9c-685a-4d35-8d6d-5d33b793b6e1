{"__type__": "cc.Json<PERSON>set", "_name": "Activity_BossGenCodeInfo", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "json": [{"type": "GComponent", "name": "ActivityBossView", "res": "ui://we0unv9bgyjzd", "children": [{"type": "GButton", "name": "CloseBtn"}, {"type": "GList", "name": "TabList"}, {"type": "GButton", "name": "startFightBtn"}, {"type": "GButton", "name": "buyBuffBtn"}, {"type": "GComponent", "name": "<PERSON><PERSON><PERSON>w", "res": "ui://we0unv9bgyjzj", "resName": "Rank<PERSON>iew", "package": "Activity_Boss", "children": [{"type": "GList", "name": "n58"}]}, {"type": "GComponent", "name": "awardView", "res": "ui://we0unv9bgyjzo", "resName": "RankAward<PERSON>iew", "package": "Activity_Boss", "children": [{"type": "GList", "name": "n42"}]}, {"type": "GButton", "name": "helpBtn"}, {"type": "GList", "name": "awardList"}]}, {"type": "GComponent", "name": "Rank<PERSON>iew", "res": "ui://we0unv9bgyjzj", "children": [{"type": "GList", "name": "n58"}]}, {"type": "GComponent", "name": "RankAward<PERSON>iew", "res": "ui://we0unv9bgyjzo", "children": [{"type": "GList", "name": "n42"}]}]}