{
    "version": "2.0.0",
    "tasks": [{
            "label": "puremvc-build-copy",
            "type": "shell",
            "command": "npx gulp build-copy",
            "isBackground": true,
            "options": {
                "cwd": "${workspaceFolder}/../core/puremvc"
            },
            "problemMatcher": []
        },
        {
            "label": "game-base-build-copy",
            "type": "shell",
            "command": "npx gulp build-copy",
            "isBackground": true,
            "options": {
                "cwd": "${workspaceFolder}/../core/game-base"
            },
            "problemMatcher": []
        },
        {
            "label": "fairygui-cocoscreator-build-copy",
            "type": "shell",
            "command": "npx gulp build-copy",
            "isBackground": true,
            "options": {
                "cwd": "${workspaceFolder}/../core/FairyGUI-cocoscreator_3.x/source"
            },
            "problemMatcher": []
        },
        {
            "label": "all-build-copy",
            "type": "shell",
            "isBackground": true,
            "dependsOn": ["puremvc-build-copy", "game-base-build-copy", "fairygui-cocoscreator-build-copy"],
            "dependsOrder": "sequence",
            "problemMatcher": []
        },
        {
            "label": "Cocos Creator compile",
            "command": "curl",
            "args": ["http://localhost:7456/asset-db/refresh"],
            "type": "shell",
            "isBackground": true,
            "group": "build",
            "presentation": {
                "reveal": "always"
            },
            "problemMatcher": []
        },
        {
            "label": "Generate Ecs Component",
            "type": "shell",
            "command": "./run",
            "group": "build",
            "options": {
                "cwd": "${workspaceFolder}/../tools/ecs/"
            },
            "presentation": {
                "reveal": "always"
            }
        },
        {
            "label": "build pb",
            "type": "shell",
            "command": "npm run buildpb",
            "group": "build",
        }
    ]
}
