{"__type__": "cc.Json<PERSON>set", "_name": "AchievementGenCodeInfo", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "json": [{"type": "GComponent", "name": "AchievementInfoItem", "res": "ui://5ua6f6joemk41m", "children": [{"type": "GComponent", "name": "proBar", "res": "ui://5ua6f6joqsem12", "resName": "AchievementProBarCmp", "package": "Achievement", "children": [{"type": "GButton", "name": "jump2InfoBtn"}]}, {"type": "GList", "name": "IconsList"}]}, {"type": "GComponent", "name": "CurFilterBtn", "res": "ui://5ua6f6joemk41p", "children": [{"type": "GComponent", "name": "FilterBtnList", "res": "ui://5ua6f6joemk41q", "resName": "FilterBtnList", "package": "Achievement", "children": [{"type": "GList", "name": "FilterBtnList"}]}]}, {"type": "GComponent", "name": "FilterBtnList", "res": "ui://5ua6f6joemk41q", "children": [{"type": "GList", "name": "FilterBtnList"}]}, {"type": "GComponent", "name": "AchievementItem", "res": "ui://5ua6f6joqsem11", "children": [{"type": "GComponent", "name": "proBar", "res": "ui://5ua6f6joqsem12", "resName": "AchievementProBarCmp", "package": "Achievement", "children": [{"type": "GButton", "name": "jump2InfoBtn"}]}]}, {"type": "GComponent", "name": "AchievementProBarCmp", "res": "ui://5ua6f6joqsem12", "children": [{"type": "GButton", "name": "jump2InfoBtn"}]}, {"type": "GComponent", "name": "AchievementProBarPanel", "res": "ui://5ua6f6joqsem15", "children": [{"type": "GComponent", "name": "proBarTotal", "res": "ui://5ua6f6joqsem12", "resName": "AchievementProBarCmp", "package": "Achievement", "children": [{"type": "GButton", "name": "jump2InfoBtn"}]}, {"type": "GComponent", "name": "proBarComplex", "res": "ui://5ua6f6joqsem12", "resName": "AchievementProBarCmp", "package": "Achievement", "children": [{"type": "GButton", "name": "jump2InfoBtn"}]}, {"type": "GComponent", "name": "proBarTask", "res": "ui://5ua6f6joqsem12", "resName": "AchievementProBarCmp", "package": "Achievement", "children": [{"type": "GButton", "name": "jump2InfoBtn"}]}, {"type": "GComponent", "name": "proBarFight", "res": "ui://5ua6f6joqsem12", "resName": "AchievementProBarCmp", "package": "Achievement", "children": [{"type": "GButton", "name": "jump2InfoBtn"}]}, {"type": "GComponent", "name": "proBarRole", "res": "ui://5ua6f6joqsem12", "resName": "AchievementProBarCmp", "package": "Achievement", "children": [{"type": "GButton", "name": "jump2InfoBtn"}]}, {"type": "GComponent", "name": "proBarInstance", "res": "ui://5ua6f6joqsem12", "resName": "AchievementProBarCmp", "package": "Achievement", "children": [{"type": "GButton", "name": "jump2InfoBtn"}]}]}, {"type": "GComponent", "name": "AchievementView", "res": "ui://5ua6f6joqsemt", "children": [{"type": "GComponent", "name": "bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GList", "name": "tabList"}, {"type": "GButton", "name": "btnGetReward"}, {"type": "GComponent", "name": "proBarPanel", "res": "ui://5ua6f6joqsem15", "resName": "AchievementProBarPanel", "package": "Achievement", "children": [{"type": "GComponent", "name": "proBarTotal", "res": "ui://5ua6f6joqsem12", "resName": "AchievementProBarCmp", "package": "Achievement", "children": [{"type": "GButton", "name": "jump2InfoBtn"}]}, {"type": "GComponent", "name": "proBarComplex", "res": "ui://5ua6f6joqsem12", "resName": "AchievementProBarCmp", "package": "Achievement", "children": [{"type": "GButton", "name": "jump2InfoBtn"}]}, {"type": "GComponent", "name": "proBarTask", "res": "ui://5ua6f6joqsem12", "resName": "AchievementProBarCmp", "package": "Achievement", "children": [{"type": "GButton", "name": "jump2InfoBtn"}]}, {"type": "GComponent", "name": "proBarFight", "res": "ui://5ua6f6joqsem12", "resName": "AchievementProBarCmp", "package": "Achievement", "children": [{"type": "GButton", "name": "jump2InfoBtn"}]}, {"type": "GComponent", "name": "proBarRole", "res": "ui://5ua6f6joqsem12", "resName": "AchievementProBarCmp", "package": "Achievement", "children": [{"type": "GButton", "name": "jump2InfoBtn"}]}, {"type": "GComponent", "name": "proBarInstance", "res": "ui://5ua6f6joqsem12", "resName": "AchievementProBarCmp", "package": "Achievement", "children": [{"type": "GButton", "name": "jump2InfoBtn"}]}]}, {"type": "GList", "name": "achievementList"}, {"type": "GList", "name": "achCompleteList"}, {"type": "GButton", "name": "curFilterBtn"}]}]}