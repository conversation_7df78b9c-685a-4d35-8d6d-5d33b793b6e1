[{"type": "GComponent", "name": "SettingView", "res": "ui://pd7lddzmewhs9", "children": [{"type": "GComponent", "name": "Bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GButton", "name": "SureBtn"}, {"type": "GButton", "name": "CancelBtn"}, {"type": "GComponent", "name": "BgSound", "res": "ui://pd7lddzmewhsa", "resName": "SoundItem", "package": "Setting", "children": [{"type": "GComponent", "name": "soundBar", "res": "ui://pd7lddzmewhsb", "resName": "SoundProgressBar", "package": "Setting", "children": [{"type": "GComponent", "name": "bar", "res": "ui://pd7lddzmewhsf", "resName": "BarReplace", "package": "Setting"}]}, {"type": "GButton", "name": "soundCheckbtn"}]}, {"type": "GComponent", "name": "SFXSound", "res": "ui://pd7lddzmewhsa", "resName": "SoundItem", "package": "Setting", "children": [{"type": "GComponent", "name": "soundBar", "res": "ui://pd7lddzmewhsb", "resName": "SoundProgressBar", "package": "Setting", "children": [{"type": "GComponent", "name": "bar", "res": "ui://pd7lddzmewhsf", "resName": "BarReplace", "package": "Setting"}]}, {"type": "GButton", "name": "soundCheckbtn"}]}, {"type": "GComponent", "name": "WeaponEffect", "res": "ui://pd7lddzmewhsc", "resName": "CheckItem", "package": "Setting", "children": [{"type": "GButton", "name": "SelectCheckBtn"}]}, {"type": "GComponent", "name": "Speaker", "res": "ui://pd7lddzmewhsc", "resName": "CheckItem", "package": "Setting", "children": [{"type": "GButton", "name": "SelectCheckBtn"}]}, {"type": "GComponent", "name": "ReceiveInvite", "res": "ui://pd7lddzmewhsc", "resName": "CheckItem", "package": "Setting", "children": [{"type": "GButton", "name": "SelectCheckBtn"}]}, {"type": "GComponent", "name": "OnlineReminder", "res": "ui://pd7lddzmewhsc", "resName": "CheckItem", "package": "Setting", "children": [{"type": "GButton", "name": "SelectCheckBtn"}]}, {"type": "GComponent", "name": "AllowAddFriend", "res": "ui://pd7lddzmewhsc", "resName": "CheckItem", "package": "Setting", "children": [{"type": "GButton", "name": "SelectCheckBtn"}]}, {"type": "GComponent", "name": "ClearCache", "res": "ui://pd7lddzmewhsc", "resName": "CheckItem", "package": "Setting", "children": [{"type": "GButton", "name": "SelectCheckBtn"}]}]}, {"type": "GComponent", "name": "SoundItem", "res": "ui://pd7lddzmewhsa", "children": [{"type": "GComponent", "name": "soundBar", "res": "ui://pd7lddzmewhsb", "resName": "SoundProgressBar", "package": "Setting", "children": [{"type": "GComponent", "name": "bar", "res": "ui://pd7lddzmewhsf", "resName": "BarReplace", "package": "Setting"}]}, {"type": "GButton", "name": "soundCheckbtn"}]}, {"type": "GComponent", "name": "SoundProgressBar", "res": "ui://pd7lddzmewhsb", "children": [{"type": "GComponent", "name": "bar", "res": "ui://pd7lddzmewhsf", "resName": "BarReplace", "package": "Setting"}]}, {"type": "GComponent", "name": "CheckItem", "res": "ui://pd7lddzmewhsc", "children": [{"type": "GButton", "name": "SelectCheckBtn"}]}, {"type": "GComponent", "name": "DDTLogView", "res": "ui://pd7lddzmewhsd", "children": [{"type": "GComponent", "name": "Bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GList", "name": "LogList"}]}]