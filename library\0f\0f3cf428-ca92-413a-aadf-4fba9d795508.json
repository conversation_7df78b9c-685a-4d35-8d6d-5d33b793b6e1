{"__type__": "cc.Json<PERSON>set", "_name": "Bag_StorageGenCodeInfo", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "json": [{"type": "GComponent", "name": "BagStorageUI", "res": "ui://akjuflvsj7v4c", "children": [{"type": "GComponent", "name": "Bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GList", "name": "TitleList"}, {"type": "GList", "name": "StorgeCell"}]}, {"type": "GComponent", "name": "SecondaryPasswordAlert", "res": "ui://akjuflvsj7v4h", "children": [{"type": "GComponent", "name": "Bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GComponent", "name": "OldPassWord", "res": "ui://akjuflvsj7v4i", "resName": "ChangePassWordInput", "package": "Bag_Storage"}, {"type": "GComponent", "name": "NewPassWord", "res": "ui://akjuflvsj7v4i", "resName": "ChangePassWordInput", "package": "Bag_Storage"}, {"type": "GComponent", "name": "AgainPassWord", "res": "ui://akjuflvsj7v4i", "resName": "ChangePassWordInput", "package": "Bag_Storage"}, {"type": "GButton", "name": "BtnChange"}, {"type": "GButton", "name": "BtnCancel"}]}, {"type": "GComponent", "name": "SecondaryPasswordRelieveAlert", "res": "ui://akjuflvsj7v4n", "children": [{"type": "GComponent", "name": "Bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GButton", "name": "BtnSure"}, {"type": "GButton", "name": "BtnCancel"}]}, {"type": "GComponent", "name": "SecondaryPasswordSettingAlert", "res": "ui://akjuflvsj7v4p", "children": [{"type": "GComponent", "name": "Bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GComponent", "name": "InputPassword", "res": "ui://akjuflvsm237v", "resName": "PassWordSetinggInput", "package": "Bag_Storage"}, {"type": "GComponent", "name": "InputPasswordAngain", "res": "ui://akjuflvsm237v", "resName": "PassWordSetinggInput", "package": "Bag_Storage"}, {"type": "GButton", "name": "BtnSure"}]}, {"type": "GComponent", "name": "BagStorageInfoPage", "res": "ui://akjuflvsm23714", "children": [{"type": "GList", "name": "tabList"}, {"type": "GComponent", "name": "ResHeaderComp", "res": "ui://dyypinptsri345w8q", "resName": "ResHeaderComp", "package": "Comm_Comp", "children": [{"type": "GList", "name": "list"}]}, {"type": "GComponent", "name": "cellList", "res": "ui://akjuflvstpoo1q", "resName": "ArticelCellListPage", "package": "Bag_Storage", "children": [{"type": "GList", "name": "cellList"}]}, {"type": "GButton", "name": "ClearupBtn"}, {"type": "GButton", "name": "PasswordBtn"}, {"type": "GButton", "name": "ArticleRefineBtn"}, {"type": "GButton", "name": "ChangeBtn"}]}, {"type": "GComponent", "name": "ArticleRefineUI", "res": "ui://akjuflvsm237w", "children": [{"type": "GComponent", "name": "Bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GButton", "name": "BtnHelp"}, {"type": "GButton", "name": "BtnRefineItem1"}, {"type": "GButton", "name": "BtnRefineItem2"}, {"type": "GButton", "name": "BtnRefineItem3"}, {"type": "GButton", "name": "BtnRefineItem4"}, {"type": "GButton", "name": "BtnRefineItem5"}, {"type": "GButton", "name": "BtnRefine"}]}, {"type": "GComponent", "name": "SecondaryPasswordTipsAlert", "res": "ui://akjuflvstpoo1l", "children": [{"type": "GComponent", "name": "Bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GButton", "name": "BtnSet"}, {"type": "GButton", "name": "BtnRelieve"}, {"type": "GButton", "name": "BtnChange"}]}, {"type": "GComponent", "name": "StorageCellListPage", "res": "ui://akjuflvstpoo1p", "children": [{"type": "GList", "name": "cellList"}]}, {"type": "GComponent", "name": "ArticelCellListPage", "res": "ui://akjuflvstpoo1q", "children": [{"type": "GList", "name": "cellList"}]}]}