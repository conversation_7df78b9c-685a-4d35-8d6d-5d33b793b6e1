{"__type__": "cc.Json<PERSON>set", "_name": "RoleRegisterGenCodeInfo", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "json": [{"type": "GComponent", "name": "RoleRegisterView", "res": "ui://zo6mhz3vhr631", "children": [{"type": "GButton", "name": "femaleBlankBtn"}, {"type": "GButton", "name": "maleBlankBtn"}, {"type": "GButton", "name": "femaleBtn"}, {"type": "GButton", "name": "maleBtn"}, {"type": "GButton", "name": "randomBtn"}, {"type": "GButton", "name": "enterBtn"}, {"type": "GButton", "name": "clearBtn"}]}]}