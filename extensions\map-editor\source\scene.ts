import { join } from 'path';

// 临时在当前模块增加编辑器内的模块为搜索路径，为了能够正常 require 到 cc 模块，后续版本将优化调用方式
module.paths.push(join(Editor.App.path, 'node_modules'));

// 当前版本需要在 module.paths 修改后才能正常使用 cc 模块
// 并且如果希望正常显示 cc 的定义，需要手动将 engine 文件夹里的 cc.d.ts 添加到插件的 tsconfig 里
// 当前版本的 cc 定义文件可以在当前项目的 temp/declarations/cc.d.ts 找到
import { director,gfx,Vec3,Node,Prefab, Sprite, resources,SpriteFrame,assetManager, RenderRoot2D, Vec2, JsonAsset, Texture2D, RenderTexture, Camera, instantiate, Layers, ImageAsset, DirectorEvent, Director, Vec4, Color, Material} from 'cc';
import { AssetInfo } from '@cocos/creator-types/editor/packages/asset-db/@types/public';
import { dir } from 'console';
import { isMainThread } from 'worker_threads';
import { render } from 'vue';
import { Message } from 'protobufjs';
import { isArrayBufferView } from 'util/types';
import { Asset } from '@cocos/creator-types/editor/packages/asset-db/@types/protected';

export function load() { };

export function unload() { };

export const methods = {

    async createSprite(url:string , fileName:string):Promise<Node|null>{
        let name =  fileName.split(".")[0];
        let path = `${url}/image/${fileName}/spriteFrame`;
        let uuid = await Editor.Message.request('asset-db', 'query-uuid', path);
        if(uuid == null || uuid == ''){
            return null;
        }
        let node = new Node(name);
        let spriteCmp = node.addComponent(Sprite)
        await new Promise<void>((resolve, reject) => {
            assetManager.loadAny({ uuid: uuid}, (err, asset) => {
                if (err) {
                    console.error('加载SpriteFrame失败:', err);
                    reject(err);
                    return;
                }
                const spriteFrame = asset as SpriteFrame;
                spriteCmp.spriteFrame = spriteFrame;
                resolve();
            });
        });
        return node;
    },

    initMap(map:any ,mapConfig:any){
        map.mapId = mapConfig.ID;
        map.size = new Vec2(mapConfig.ForegroundWidth,mapConfig.ForegroundHeight);
    },

    async getMapConfig(mapId:number):Promise<any>{
        let mapConfig = null;
        await new Promise<void>((resolve, reject) => {
            //加载MapCofnig db://assets/debug/autoGen/tableJsons/game_map_game_maptable.json
            let uuid = 'c066b532-2579-4e03-95af-c7397427af26';
            assetManager.loadAny({ uuid: uuid}, (err, asset) => {
                if (err) {
                    console.error('加载MapConfig失败:', err);
                    reject(err);
                    return;
                }
                const jsonAsset = asset as JsonAsset;
                jsonAsset.json!.forEach((data:any) => {
                    if(data.ID != mapId) return;
                    mapConfig = data;
                });
                resolve();
            });
        });
        return mapConfig;
    },

    async createMapPrefab(url: string){
        console.log(`创建地图Prefab：${url}`)
        let urlParts = url.split("/");
        let mapId = urlParts[urlParts.length - 1];
        let parent = director.getScene();

        let prefabRoot = new Node(mapId)
        prefabRoot.addComponent(RenderRoot2D)

        let mapConfig = await this.getMapConfig(parseInt(mapId));
        let mapW = mapConfig.ForegroundWidth;
        let mapH = mapConfig.ForegroundHeight;

        let map = prefabRoot.addComponent("MapComponent")
        this.initMap(map,mapConfig)
        parent?.addChild(prefabRoot);

        let backGroundNode = new Node("Background");
        backGroundNode.setPosition(0,0,0);
        prefabRoot.addChild(backGroundNode);

        let excavateLand = new Node("Excavate_Land");
        excavateLand.setPosition(0,0,0);
        prefabRoot.addChild(excavateLand);

        let noExcavateLand = new Node("No_Excavate_Land");
        noExcavateLand.setPosition(0,0,0);
        prefabRoot.addChild(noExcavateLand);

        let living = new Node("Living");
        backGroundNode.setPosition(0,0,0);
        prefabRoot.addChild(living);

        let back = await this.createSprite(url, "back.jpg");
        if(back != null){
            back.setScale(1.5,1.5)
            back.setPosition(mapW/2,mapH/2);
            backGroundNode.addChild(back);
        }

        let fore = await this.createSprite(url, "fore.png");
        if(fore != null){
            fore.setPosition(mapW/2,mapH/2);
            excavateLand.addChild(fore);
            await new Promise<void>((resolve, reject) => {
                //加载 digHole 材质   db://assets/resources/material/digHole.mtl
                assetManager.loadAny({ uuid: "2cIshUgC5ODIxRCuCfiGyV"}, (err, asset) => {
                    if (err) {
                        return;
                    }
                    const material = asset as Material;
                    let spriteComp = fore.getComponent(Sprite);
                    if(spriteComp == null){
                        return;
                    }
                    spriteComp.customMaterial = material;
                    resolve();
                });
            });
        }

        let dead = await this.createSprite(url, "dead.png");
        if(dead != null){
            dead.setPosition(mapW/2,mapH/2);
            noExcavateLand.addChild(dead);
        }
        
        let savePath = `${url}/prefab_${mapId}.prefab`;
        let info = await Editor.Message.request('asset-db', 'query-asset-info', savePath);
        if(info != null){
            await Editor.Message.request('asset-db', 'delete-asset', savePath);
        }
        await Editor.Message.request('scene', 'create-prefab', prefabRoot.uuid, savePath);

        let newNode = parent?.getChildByName(mapId);
        newNode?.removeFromParent();
    },

    async exportTerrainData(mapPrefabUrl: string):Promise<void>{
        console.log(`导出地形数据：${mapPrefabUrl}`)
        let uuid = await Editor.Message.request('asset-db', 'query-uuid', mapPrefabUrl);
        if(uuid == null){
            return;
        }
        let rootNode = director.getScene() as Node;
        let mapNode;
        await new Promise<void>((resolve, reject) => {
            assetManager.loadAny({ uuid: uuid}, (err, prefab) => {
                if (err) {
                    console.error('加载Prefab失败:', err);
                    reject(err);
                    return;
                }
                const p = prefab as Prefab;
                mapNode = instantiate(p);
                mapNode.setParent(rootNode);
                // this.captureTerrainData(mapNode);
                resolve();
            });
        });
        if (mapNode == null) {
            return;
        }
        mapNode = mapNode as Node;
        const excavateLandRT = new RenderTexture();
        const noExcavateLandRT = new RenderTexture();
        let map:any = mapNode.getComponent("MapComponent");
        let mapId = map.mapId;
        let width = map.size.x;
        let height = map.size.y;

        excavateLandRT.reset({width: width,height: height,});
        noExcavateLandRT.reset({width: width,height: height,});

        let excavateLandLayer = Layers.Enum.UI_2D;
        let noExcavateLandLayer = Layers.Enum.UI_3D;
        
        let excavateLandCamera = this.getCamera(rootNode,width,height);
        excavateLandCamera.node.name = "excavateLandCamera"
        excavateLandCamera.targetTexture = excavateLandRT;
        excavateLandCamera.visibility = excavateLandLayer;
        let noExcavateLandCamera = this.getCamera(rootNode,width,height);
        noExcavateLandCamera.node.name = "noExcavateLandCamera"
        noExcavateLandCamera.visibility = noExcavateLandLayer;
        noExcavateLandCamera.targetTexture = noExcavateLandRT;

        let excavateLand = mapNode.getChildByName("Excavate_Land");
        if(excavateLand == null){
            return;
        }
        for(let i = 0; i < excavateLand.children.length; i++){
            let child = excavateLand.children[i];
            child.layer = excavateLandLayer;
        }

        let noExcavateLand = mapNode.getChildByName("No_Excavate_Land");
        if(noExcavateLand == null){
            return;
        }
        for(let i = 0; i < noExcavateLand.children.length; i++){
            let child = noExcavateLand.children[i];
            child.layer = noExcavateLandLayer;
        }

        director.once(Director.EVENT_AFTER_DRAW , async ()=>{
            let excavateLandBytes = excavateLandRT.readPixels();
            let noExcavateLandBytes = noExcavateLandRT.readPixels();
            if(excavateLandBytes == null || noExcavateLandBytes == null){
                console.error("excavateLandBytes or noExcavateLandBytes is null");
                return;
            }
            const mapBytes = new Uint8Array(excavateLandBytes.length/4);
            // console.log("mapBytes length = ", mapBytes.length);
            let j = 0;
            for(let i = 0; i < noExcavateLandBytes.length; i += 4) {
                let c = 0;
                if(noExcavateLandBytes[i+3] > 20){
                    c = 3;
                }
                else if(excavateLandBytes[i+3] > 20){
                    c = 2;
                }
                else{
                    c = 0
                }
                mapBytes[j++] = c;
            }
            let finalMapBytes = this.compressMapBytes(mapBytes);
            //创建JSON对象
            const jsonData = {
                mapId: mapId,
                width: width,
                height: height,
                mapBytes: Array.from(finalMapBytes) // 转换为普通数组
            };
            // // 转换为JSON字符串
            const jsonStr = JSON.stringify(jsonData);

            excavateLandCamera.node.removeFromParent();
            noExcavateLandCamera.node.removeFromParent();
            map.node.removeFromParent();
            let baseUrl = mapPrefabUrl.substring(0, mapPrefabUrl.lastIndexOf('/'));
            let jsonUrl = `${baseUrl}/${mapId}.json`;
            console.log("jsonUrl = ", jsonUrl);
            let info = await Editor.Message.request('asset-db', 'query-asset-info', jsonUrl);
            if(info != null){
                await Editor.Message.request('asset-db', 'delete-asset', jsonUrl);
            }
            await Editor.Message.request('asset-db', 'create-asset', jsonUrl, jsonStr);

        })
        director.tick(0);
    },

    async exportCraterData(url: string){
        console.log(`导出弹坑数据：${url}`)
        let uuid = await Editor.Message.request('asset-db', 'query-uuid', url);
        if(uuid == null || uuid == ''){
            return;
        }

        let tempCamera = new Node("TempCamera");
        tempCamera.setParent(director.getScene());
        tempCamera.setPosition(0,0);
        tempCamera.addComponent(RenderRoot2D);
        let camera = tempCamera.addComponent(Camera);
        camera.visibility = Layers.Enum.UI_2D;
        camera.clearFlags |= Camera.ClearFlag.SOLID_COLOR;
        camera.clearColor = new Color(0,0,0,0);
        camera.projection = Camera.ProjectionType.ORTHO;
        camera.near = 0;

        let node = new Node("CraterNode");
        node.layer = Layers.Enum.UI_2D; 
        let spriteCmp = node.addComponent(Sprite)
        await new Promise<void>((resolve, reject) => {
            assetManager.loadAny({ uuid: uuid}, (err, asset) => {
                if (err) {
                    console.error('加载SpriteFrame失败:', err);
                    reject(err);
                    return;
                }
                const spriteFrame = asset as SpriteFrame;
                spriteCmp.spriteFrame = spriteFrame;
                resolve();
            });
        });
        if(spriteCmp.spriteFrame == null){
            return;
        }
        let width = spriteCmp.spriteFrame.width;
        let height = spriteCmp.spriteFrame.height;
        camera.orthoHeight = height/2;
        tempCamera.addChild(node);

        const rt = new RenderTexture();
        rt.reset({width: width,height: height,});
        camera.targetTexture = rt;

        director.once(Director.EVENT_AFTER_DRAW , async ()=>{
            let pixelsBytes = rt.readPixels();
            if(pixelsBytes == null){
                console.error("excavateLandBytes or noExcavateLandBytes is null");
                return;
            }
            let bytes = new Uint8Array(pixelsBytes.length/4);
            for(let i = 0; i < pixelsBytes.length; i+=4){
                if(pixelsBytes[i+3] > 0){
                    bytes[i/4] = 1;
                }
            }
            let finalBytes = this.compressMapBytes(bytes);
            //创建JSON对象
            const jsonData = {
                width: width,
                height: height,
                bytes: Array.from(finalBytes) // 转换为普通数组
            };
            // // 转换为JSON字符串
            const jsonStr = JSON.stringify(jsonData);
            tempCamera.removeFromParent();
            let jsonUrl = url.replace(".png/spriteFrame","Bytes.json");
            let info = await Editor.Message.request('asset-db', 'query-asset-info', jsonUrl);
            if(info != null){
                await Editor.Message.request('asset-db', 'delete-asset', jsonUrl);
            }
            await Editor.Message.request('asset-db', 'create-asset', jsonUrl, jsonStr);
        })
        director.tick(0);

    },

    compressMapBytes(mapBytes:Uint8Array): number[] {
        let lastVal = mapBytes[0];
        let lastValCount = 1;
        const list: number[] = [];
        let index = 0;
        for (let i = 1; i < mapBytes.length; i++) {
            if (lastVal === mapBytes[i]) {
                lastValCount++;
            } else {
                list.push(lastVal);
                list.push(lastValCount);
                index += lastValCount;
                lastVal = mapBytes[i];
                lastValCount = 1;
            }
        }
        if (index < mapBytes.length) { // 还有部分没有记录
            list.push(lastVal);
            list.push(lastValCount);
            index += lastValCount;
        }
        return list;
    },
    getCamera(parent:Node, width:number, height:number){
        let tempCamera = new Node("tempCamera");
        tempCamera.setParent(director.getScene());
        tempCamera.setPosition(width/2,height/2);
        let camera = tempCamera.addComponent(Camera);
        camera.visibility = Layers.Enum.ALL;
        camera.clearFlags |= Camera.ClearFlag.SOLID_COLOR;
        camera.clearColor = new Color(0,0,0,0);
        camera.orthoHeight = height / 2;
        camera.projection = Camera.ProjectionType.ORTHO;
        camera.near = 0;
        camera.far = 1000;
        camera.priority = 100;
        return camera;
    },
    getFinalPixels(rt: RenderTexture): Uint8Array | null{
        let pixels = rt.readPixels();
        if(pixels) {
            // 创建翻转后的像素数据
            const flippedPixels = new Uint8Array(pixels.length);
            const bytesPerRow = rt.width * 4;
            for(let y = 0; y < rt.height; y++) {
                const srcRow = y * bytesPerRow;
                const dstRow = (rt.height - 1 - y) * bytesPerRow;
                flippedPixels.set(pixels.subarray(srcRow, srcRow + bytesPerRow), dstRow);
            }
            return flippedPixels;
        }
        return null;
    },
};
