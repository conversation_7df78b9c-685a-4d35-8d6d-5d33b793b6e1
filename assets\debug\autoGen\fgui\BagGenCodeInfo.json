[{"type": "GComponent", "name": "GiftBoxPage", "res": "ui://r2lxrneddc2845w8y", "children": [{"type": "GComponent", "name": "n0", "res": "ui://r2lxrnedrwq519", "resName": "RoleInfoPage", "package": "Bag", "children": [{"type": "GButton", "name": "n19"}, {"type": "GButton", "name": "n21"}, {"type": "GButton", "name": "title_comp"}, {"type": "GComponent", "name": "attr_comp", "res": "ui://r2lxrnedrwq51b", "resName": "role_attr_comp", "package": "Bag", "children": [{"type": "GList", "name": "attr_progress_list"}, {"type": "GList", "name": "attr_list"}]}, {"type": "GButton", "name": "btn_blacksmith"}]}]}, {"type": "GComponent", "name": "BagEquipTipView", "res": "ui://r2lxrnedindu45wcm", "children": [{"type": "GList", "name": "opt_list"}, {"type": "GList", "name": "list"}]}, {"type": "GComponent", "name": "BagView", "res": "ui://r2lxrnednv7qr45wc6", "children": [{"type": "GComponent", "name": "Bg", "res": "ui://dyypinptj7v45wbk", "resName": "AlertBg", "package": "Comm_Comp", "children": [{"type": "GButton", "name": "CloseButton"}]}, {"type": "GList", "name": "tabType"}]}, {"type": "GComponent", "name": "RoleInfoPage", "res": "ui://r2lxrnedrwq519", "children": [{"type": "GButton", "name": "n19"}, {"type": "GButton", "name": "n21"}, {"type": "GButton", "name": "title_comp"}, {"type": "GComponent", "name": "attr_comp", "res": "ui://r2lxrnedrwq51b", "resName": "role_attr_comp", "package": "Bag", "children": [{"type": "GList", "name": "attr_progress_list"}, {"type": "GList", "name": "attr_list"}]}, {"type": "GButton", "name": "btn_blacksmith"}]}, {"type": "GComponent", "name": "role_attr_comp", "res": "ui://r2lxrnedrwq51b", "children": [{"type": "GList", "name": "attr_progress_list"}, {"type": "GList", "name": "attr_list"}]}, {"type": "GComponent", "name": "BagInfoPage", "res": "ui://r2lxrnedsri345w8l", "children": [{"type": "GComponent", "name": "ResHeaderComp", "res": "ui://dyypinptsri345w8q", "resName": "ResHeaderComp", "package": "Comm_Comp", "children": [{"type": "GList", "name": "list"}]}, {"type": "GList", "name": "tabList"}, {"type": "GButton", "name": "ClearupBtn"}, {"type": "GButton", "name": "PasswordBtn"}, {"type": "GButton", "name": "ArticleRefineBtn"}, {"type": "GButton", "name": "ChangeBtn"}]}, {"type": "GComponent", "name": "RoleInfoEquipPage", "res": "ui://r2lxrnedto5x45wbd", "children": [{"type": "GButton", "name": "btn_achievement"}, {"type": "GButton", "name": "btn_exp"}, {"type": "GButton", "name": "btn_badge"}, {"type": "GButton", "name": "btn_fangt"}, {"type": "GButton", "name": "btn_zhufu"}, {"type": "GButton", "name": "btn_level"}, {"type": "GButton", "name": "btn_vip"}, {"type": "GButton", "name": "btn_married"}, {"type": "GButton", "name": "btn_academy"}, {"type": "GButton", "name": "btn_hide"}, {"type": "GButton", "name": "btn_add_friend"}, {"type": "GComponent", "name": "expprogress", "res": "ui://r2lxrnedxnxv45waa", "resName": "exp_progess", "package": "Bag"}, {"type": "GComponent", "name": "fight_power_comp", "res": "ui://r2lxrnedxnxv45wac", "resName": "fight_power_comp", "package": "Bag"}, {"type": "GList", "name": "hide_list"}, {"type": "GList", "name": "part_left"}, {"type": "GList", "name": "part_right"}, {"type": "GList", "name": "part_bottom"}]}, {"type": "GComponent", "name": "BagListPage", "res": "ui://r2lxrnedto5x45wbg", "children": [{"type": "GList", "name": "cellList"}]}, {"type": "GComponent", "name": "part_toggle_Item", "res": "ui://r2lxrnedxnxv45wah", "children": [{"type": "GButton", "name": "check"}]}]